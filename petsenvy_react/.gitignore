# Dependency directories
/node_modules/

# Build output
/build/
/dist/

# Firebase local files and emulators
.firebase/
firebase-debug.log
firestore-debug.log

# Configuration files (optional to ignore)
/.firebaserc
/firebase.json

# Environment files
.env
.env.local
.env.*.local

# Lock files (optional, but you're asking to ignore these)
package-lock.json
yarn.lock
pnpm-lock.yaml

# OS-specific files
.DS_Store
Thumbs.db

# IDE/editor folders
.vscode/
.idea/
*.sublime-workspace
*.sublime-project
*.code-workspace

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*

# Optional cache directories
.cache/
.tmp/
temp/

# Test coverage
coverage/

# Misc
*.swp
*.bak
*.orig
*.rej

# GitHub Codespaces
.devcontainer/

# Windows system files
Desktop.ini
