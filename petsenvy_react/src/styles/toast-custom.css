/* Custom Toast Styles */

/* Override default toast container */
.Toastify__toast-container {
  font-family: 'Poppins', sans-serif;
}

/* Success Toast */
.toast-success.Toastify__toast--success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.toast-success .Toastify__toast-icon {
  color: white;
}

.toast-success .Toastify__progress-bar {
  background: rgba(255, 255, 255, 0.3);
}

/* Error Toast */
.toast-error.Toastify__toast--error {
  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.toast-error .Toastify__toast-icon {
  color: white;
}

.toast-error .Toastify__progress-bar {
  background: rgba(255, 255, 255, 0.3);
}

/* Warning Toast */
.toast-warning.Toastify__toast--warning {
  background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%);
  color: #212529;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.toast-warning .Toastify__toast-icon {
  color: #212529;
}

.toast-warning .Toastify__progress-bar {
  background: rgba(33, 37, 41, 0.2);
}

/* Info Toast */
.toast-info.Toastify__toast--info {
  background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%);
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.toast-info .Toastify__toast-icon {
  color: white;
}

.toast-info .Toastify__progress-bar {
  background: rgba(255, 255, 255, 0.3);
}

/* Loading Toast */
.toast-loading.Toastify__toast--default {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.toast-loading .Toastify__toast-icon {
  color: white;
}

.toast-loading .Toastify__progress-bar {
  background: rgba(255, 255, 255, 0.3);
}

/* Toast Body */
.Toastify__toast-body {
  font-size: 14px;
  line-height: 1.4;
  padding: 12px 16px;
}

/* Close Button */
.Toastify__close-button {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.Toastify__close-button:hover {
  opacity: 1;
}

/* Animation Enhancements */
.Toastify__toast {
  border-radius: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.Toastify__toast--rtl {
  direction: rtl;
}

/* Progress Bar */
.Toastify__progress-bar {
  height: 3px;
  border-radius: 0 0 8px 8px;
}

/* Mobile Responsiveness */
@media only screen and (max-width: 480px) {
  .Toastify__toast-container {
    width: 100vw;
    padding: 0;
    left: 0;
    margin: 0;
  }
  
  .Toastify__toast {
    margin-bottom: 0;
    border-radius: 0;
  }
  
  .Toastify__toast-body {
    padding: 16px;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .Toastify__toast-container {
    --toastify-color-light: #2d3748;
    --toastify-color-dark: #1a202c;
    --toastify-text-color-light: #e2e8f0;
    --toastify-text-color-dark: #f7fafc;
  }
}

/* Custom Animation */
@keyframes toastSlideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes toastSlideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.Toastify__slide-enter {
  animation: toastSlideIn 0.3s ease-out;
}

.Toastify__slide-exit {
  animation: toastSlideOut 0.3s ease-in;
}

/* Theme-specific styles */
.toast-theme-petsenvy {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* Success with pet theme */
.toast-success.toast-theme-petsenvy {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-left: 4px solid #ffffff;
}

/* Error with pet theme */
.toast-error.toast-theme-petsenvy {
  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
  border-left: 4px solid #ffffff;
}

/* Warning with pet theme */
.toast-warning.toast-theme-petsenvy {
  background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%);
  border-left: 4px solid #212529;
}

/* Info with pet theme */
.toast-info.toast-theme-petsenvy {
  background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%);
  border-left: 4px solid #ffffff;
}
