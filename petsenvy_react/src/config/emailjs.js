// EmailJS Configuration
// To set up EmailJS:
// 1. Go to https://www.emailjs.com/
// 2. Create a free account
// 3. Create a new service (Gmail, Outlook, etc.)
// 4. Create a new template with the following variables:
//    - {{from_name}} - Sender's name
//    - {{from_email}} - Sender's email
//    - {{phone}} - Sender's phone
//    - {{service}} - Selected service
//    - {{message}} - Message content
//    - {{to_email}} - Recipient email (<EMAIL>)
// 5. Get your Service ID, Template ID, and Public Key
// 6. Replace the values below with your actual credentials

export const EMAILJS_CONFIG = {
  SERVICE_ID: 'service_19kawky', // Replace with your EmailJS service ID
  TEMPLATE_ID: 'template_t5rv0ai', // Replace with your EmailJS template ID
  PUBLIC_KEY: '5zu84oo7I45eM74kp', // Replace with your EmailJS public key
  TO_EMAIL: '<EMAIL>' // Owner's email address
};

// Example template content for EmailJS:
/*
Subject: New Contact Form Submission from PetsEnvy Website

Hello,

You have received a new message from the PetsEnvy website contact form:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Service Interest: {{service}}

Message:
{{message}}

Please respond to this inquiry as soon as possible.

Best regards,
PetsEnvy Website
*/
