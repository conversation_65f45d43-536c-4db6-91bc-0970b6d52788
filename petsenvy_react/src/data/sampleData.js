// Sample data for initializing Firebase collections

export const sampleProducts = [
  {
    id: 'prod_001',
    name: 'Premium Dog Food',
    description: 'High-quality nutrition for your beloved dog with natural ingredients.',
    price: 49.99,
    category: 'Food',
    image: '/img/shop/shop1.png',
    inStock: true,
    stockQuantity: 50,
    rating: 4.5,
    reviews: 23
  },
  {
    id: 'prod_002',
    name: 'Cat Toy Set',
    description: 'Interactive toy set to keep your cat entertained and active.',
    price: 24.99,
    category: 'Toys',
    image: '/img/shop/shop2.png',
    inStock: true,
    stockQuantity: 30,
    rating: 4.2,
    reviews: 15
  },
  {
    id: 'prod_003',
    name: 'Pet Carrier Bag',
    description: 'Comfortable and secure carrier bag for traveling with your pet.',
    price: 79.99,
    category: 'Accessories',
    image: '/img/shop/shop3.png',
    inStock: true,
    stockQuantity: 20,
    rating: 4.7,
    reviews: 31
  },
  {
    id: 'prod_004',
    name: '<PERSON> Leash & Collar Set',
    description: 'Durable and stylish leash and collar set for daily walks.',
    price: 34.99,
    category: 'Accessories',
    image: '/img/shop/shop4.png',
    inStock: true,
    stockQuantity: 40,
    rating: 4.3,
    reviews: 18
  },
  {
    id: 'prod_005',
    name: 'Pet Grooming Kit',
    description: 'Complete grooming kit with all essential tools for pet care.',
    price: 89.99,
    category: 'Grooming',
    image: '/img/shop/shop5.png',
    inStock: true,
    stockQuantity: 15,
    rating: 4.6,
    reviews: 27
  },
  {
    id: 'prod_006',
    name: 'Cat Scratching Post',
    description: 'Multi-level scratching post to keep your cat\'s claws healthy.',
    price: 59.99,
    category: 'Furniture',
    image: '/img/shop/shop6.png',
    inStock: true,
    stockQuantity: 25,
    rating: 4.4,
    reviews: 12
  }
];

export const sampleServices = [
  {
    id: 'serv_001',
    name: 'Pet Grooming',
    description: 'Professional grooming service including bath, nail trimming, and styling.',
    price: 65.00,
    duration: '2 hours',
    category: 'Grooming',
    image: '/img/service/service1.png',
    available: true,
    features: ['Bath & Dry', 'Nail Trimming', 'Ear Cleaning', 'Styling']
  },
  {
    id: 'serv_002',
    name: 'Veterinary Checkup',
    description: 'Comprehensive health checkup by certified veterinarians.',
    price: 120.00,
    duration: '1 hour',
    category: 'Healthcare',
    image: '/img/service/service2.png',
    available: true,
    features: ['Physical Examination', 'Vaccination', 'Health Report', 'Consultation']
  },
  {
    id: 'serv_003',
    name: 'Pet Training',
    description: 'Professional training sessions for behavior improvement and obedience.',
    price: 85.00,
    duration: '1.5 hours',
    category: 'Training',
    image: '/img/service/service3.png',
    available: true,
    features: ['Basic Commands', 'Behavior Training', 'Socialization', 'Progress Report']
  },
  {
    id: 'serv_004',
    name: 'Pet Boarding',
    description: '24/7 pet boarding service with comfortable accommodation.',
    price: 45.00,
    duration: 'Per day',
    category: 'Boarding',
    image: '/img/service/service4.png',
    available: true,
    features: ['24/7 Care', 'Comfortable Rooms', 'Play Time', 'Feeding Service']
  },
  {
    id: 'serv_005',
    name: 'Pet Photography',
    description: 'Professional photography session to capture beautiful moments.',
    price: 150.00,
    duration: '2 hours',
    category: 'Photography',
    image: '/img/service/service5.png',
    available: true,
    features: ['Professional Photos', 'Multiple Poses', 'Digital Copies', 'Print Options']
  }
];

export const sampleSubscriptionPlans = [
  {
    id: 'plan_001',
    name: 'Basic Care',
    description: 'Essential pet care services for budget-conscious pet owners.',
    price: 29.99,
    billingCycle: 'monthly',
    features: [
      'Monthly health checkup',
      'Basic grooming service',
      '10% discount on products',
      'Email support'
    ],
    popular: false,
    available: true
  },
  {
    id: 'plan_002',
    name: 'Premium Care',
    description: 'Comprehensive pet care with additional benefits and services.',
    price: 59.99,
    billingCycle: 'monthly',
    features: [
      'Bi-weekly health checkups',
      'Full grooming service',
      '20% discount on products',
      'Priority booking',
      'Phone support',
      'Free nail trimming'
    ],
    popular: true,
    available: true
  },
  {
    id: 'plan_003',
    name: 'Ultimate Care',
    description: 'Premium pet care package with all services included.',
    price: 99.99,
    billingCycle: 'monthly',
    features: [
      'Weekly health checkups',
      'Full grooming & spa service',
      '30% discount on products',
      'Priority booking',
      '24/7 support',
      'Free training sessions',
      'Emergency care',
      'Pet insurance included'
    ],
    popular: false,
    available: true
  },
  {
    id: 'plan_004',
    name: 'Annual Basic',
    description: 'Annual subscription with significant savings on basic care.',
    price: 299.99,
    billingCycle: 'yearly',
    features: [
      'Monthly health checkup',
      'Basic grooming service',
      '15% discount on products',
      'Email support',
      '2 months free'
    ],
    popular: false,
    available: true
  },
  {
    id: 'plan_005',
    name: 'Annual Premium',
    description: 'Annual premium subscription with maximum value and savings.',
    price: 599.99,
    billingCycle: 'yearly',
    features: [
      'Bi-weekly health checkups',
      'Full grooming service',
      '25% discount on products',
      'Priority booking',
      'Phone support',
      'Free nail trimming',
      '2 months free'
    ],
    popular: true,
    available: true
  }
];

// Database schema structure for reference
export const databaseSchema = {
  users: {
    // Document ID: user.uid
    uid: 'string',
    email: 'string',
    displayName: 'string',
    createdAt: 'timestamp',
    updatedAt: 'timestamp',
    subscriptions: 'array', // Array of subscription IDs
    orders: 'array', // Array of order IDs
    services: 'array' // Array of service booking IDs
  },
  
  products: {
    // Document ID: auto-generated or custom
    name: 'string',
    description: 'string',
    price: 'number',
    category: 'string',
    image: 'string',
    inStock: 'boolean',
    stockQuantity: 'number',
    rating: 'number',
    reviews: 'number',
    createdAt: 'timestamp',
    updatedAt: 'timestamp'
  },
  
  services: {
    // Document ID: auto-generated or custom
    name: 'string',
    description: 'string',
    price: 'number',
    duration: 'string',
    category: 'string',
    image: 'string',
    available: 'boolean',
    features: 'array',
    createdAt: 'timestamp',
    updatedAt: 'timestamp'
  },
  
  subscriptionPlans: {
    // Document ID: auto-generated or custom
    name: 'string',
    description: 'string',
    price: 'number',
    billingCycle: 'string', // 'monthly' or 'yearly'
    features: 'array',
    popular: 'boolean',
    available: 'boolean',
    createdAt: 'timestamp',
    updatedAt: 'timestamp'
  },
  
  subscriptions: {
    // Document ID: auto-generated
    userId: 'string',
    planId: 'string',
    status: 'string', // 'active', 'cancelled', 'expired'
    createdAt: 'timestamp',
    nextBillingDate: 'timestamp',
    cancelledAt: 'timestamp' // optional
  },
  
  orders: {
    // Document ID: auto-generated
    userId: 'string',
    items: 'array', // Array of {productId, quantity, price}
    total: 'number',
    status: 'string', // 'pending', 'processing', 'shipped', 'delivered', 'cancelled'
    shippingAddress: 'object',
    paymentMethod: 'string',
    createdAt: 'timestamp',
    updatedAt: 'timestamp'
  },
  
  serviceBookings: {
    // Document ID: auto-generated
    userId: 'string',
    serviceId: 'string',
    bookingDate: 'timestamp',
    status: 'string', // 'pending', 'confirmed', 'completed', 'cancelled'
    notes: 'string',
    createdAt: 'timestamp'
  },
  
  carts: {
    // Document ID: user.uid
    items: 'array', // Array of {productId, quantity, price, name, image}
    updatedAt: 'timestamp'
  }
};
