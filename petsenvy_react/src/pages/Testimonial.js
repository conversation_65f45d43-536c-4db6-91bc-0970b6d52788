import React from 'react';
import { Link } from 'react-router-dom';

const Testimonial = () => {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      profession: "Graphic Designer",
      image: "/img/testimonial/mani (2).jpeg",
      review: "I've never seen my cat so calm during grooming! The staff is gentle, professional, and truly love animals. Highly recommended!"
    },
    {
      id: 2,
      name: "<PERSON>",
      profession: "Bank Manager",
      image: "/img/testimonial/mani (2).jpeg",
      review: "Excellent care and friendly service! Their pet taxi service saved me so much time. My dog arrived happy and healthy every time."
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON>",
      profession: "School Teacher",
      image: "/img/testimonial/mani (2).jpeg",
      review: "Their pet hotel is clean, well-managed, and my Labrador always comes back happy. I trust them completely with my furry friend."
    },
    {
      id: 4,
      name: "<PERSON><PERSON>",
      profession: "Software Engineer",
      image: "/img/testimonial/mani (2).jpeg",
      review: "The team is so cooperative and skilled. Their training sessions helped my puppy behave better within a week. Truly grateful!"
    },
    {
      id: 5,
      name: "<PERSON><PERSON>",
      profession: "Entrepreneur",
      image: "/img/testimonial/mani (2).jpeg",
      review: "Very reliable and kind people! I was nervous at first, but now I can't imagine anyone else caring for my pets."
    },
    {
      id: 6,
      name: "Hamza Khan",
      profession: "Business Owner",
      image: "/img/testimonial/mani (2).jpeg",
      review: "Outstanding service quality! The wellness checkups are thorough and the staff explains everything clearly. Five stars!"
    },
    {
      id: 7,
      name: "Fatima Ahmed",
      profession: "Doctor",
      image: "/img/testimonial/mani (2).jpeg",
      review: "Professional and caring service. My Persian cat loves coming here for grooming sessions. The results are always amazing!"
    },
    {
      id: 8,
      name: "Ahmed Hassan",
      profession: "Marketing Manager",
      image: "/img/testimonial/mani (2).jpeg",
      review: "The adoption process was smooth and transparent. They helped us find the perfect companion for our family. Thank you!"
    },
    {
      id: 9,
      name: "Sara Khan",
      profession: "Architect",
      image: "/img/testimonial/mani (2).jpeg",
      review: "Reliable walking and sitting services. I can travel worry-free knowing my pets are in such caring hands. Highly recommend!"
    }
  ];

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>Testimonials</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span>Testimonials</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonial Area */}
      <section id="testimonial_area" className="section_padding main_test_style">
        <div className="container">
          {/* Section Heading */}
          <div className="row">
            <div className="col-lg-6 offset-lg-3">
              <div className="section_heading">
                <h2>Clients Review</h2>
                <p>Hear what our happy pet parents have to say about their experiences. Real stories, real care, and real results from those who trust us most.</p>
              </div>
            </div>
          </div>
          {/* Inner Content */}
          <div className="row">
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="col-lg-4 col-md-6 col-sm-12 col-12">
                <div className="testimonial_area_item">
                  <img src={testimonial.image} className="test_main_img" alt="img" />
                  <p className="test_main_para">{testimonial.review}</p>
                  <img src="/img/testimonial/quote.png" className="test_quote_img" alt="icon" />
                  <div className="test_destination">
                    <h3>{testimonial.name}</h3>
                    <p>{testimonial.profession}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Testimonial;
