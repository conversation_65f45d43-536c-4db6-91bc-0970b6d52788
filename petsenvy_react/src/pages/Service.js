import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useModal } from '../contexts/ModalContext';
import { useBooking } from '../contexts/BookingContext';
import { servicesServices } from '../services/firebaseServices';
import { initializeDatabase } from '../utils/initializeDatabase';
import { showSuccessToast, showErrorToast, showWarningToast } from '../utils/toast';

const Service = () => {
  const { currentUser } = useAuth();
  const { showInfoModal, showWarningModal, showSuccessModal, showConfirmModal } = useModal();
  const { bookService, loading: bookingLoading } = useBooking();
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [bookingService, setBookingService] = useState({}); // Track booking state per service

  useEffect(() => {
    loadServices();
  }, []);

  const loadServices = async () => {
    try {
      setLoading(true);
      setError('');

      // Initialize database if needed
      await initializeDatabase();

      // Load services from Firebase
      const servicesData = await servicesServices.getAllServices();
      setServices(servicesData);
    } catch (error) {
      console.error('Error loading services:', error);
      setError('Failed to load services. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const handleBookService = async (service) => {
    if (!currentUser) {
      showWarningModal(
        'Login Required',
        'Please log in to book a service.',
        () => {
          window.location.href = '/my-account';
        }
      );
      return;
    }

    showConfirmModal(
      'Confirm Service Booking',
      `Are you sure you want to book "${service.name}" for ${formatPrice(service.price)}?`,
      async () => {
        try {
          setBookingService(prev => ({ ...prev, [service.id]: true }));
          await bookService(service);
          showSuccessModal(
            'Service Booked Successfully!',
            `Your booking for "${service.name}" has been confirmed. You can view and manage your bookings in your dashboard.`
          );
        } catch (error) {
          console.error('Error booking service:', error);
          showErrorToast('Failed to book service. Please try again.');
        } finally {
          setBookingService(prev => ({ ...prev, [service.id]: false }));
        }
      }
    );
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>Our Services</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span> Services</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Area */}
      <section id="main_service" className="section_padding">
        <div className="container">
          {/* Section Heading */}
          <div className="row">
            <div className="col-lg-6 offset-lg-3">
              <div className="section_heading">
                <h2>Our pet care services</h2>
                <p>We offer gentle, expert care tailored to your pet's needs. From grooming to health checkups, every visit brings comfort and compassion.</p>
              </div>
            </div>
          </div>
          {/* Inner Content */}
          <div className="row">
            {error && (
              <div className="col-12">
                <div className="alert alert-danger text-center">
                  <p>{error}</p>
                  <button className="btn btn_theme" onClick={loadServices}>
                    Try Again
                  </button>
                </div>
              </div>
            )}

            {services.map((service, index) => (
              <div key={service.id} className="col-lg-4 col-md-6 col-sm-12 col-12">
                <div className="main_service_item_wrapper">
                  <img src="/img/element/service.png" className="service_hover_right" alt="icon" />
                  <div className="main_service_item_icon">
                    <img src={service.image || `/img/icon/service-${(index % 3) + 1}.png`} alt="icon" />
                    <h3>{service.name}</h3>
                  </div>
                  <div className="main_service_inner_content">
                    <p>{service.description}</p>
                    <div className="main_Service_price">
                      <h3>{formatPrice(service.price)}</h3>
                      <small className="text-muted">Duration: {service.duration}</small>
                    </div>
                    <div className="service_features mb-3">
                      {service.features && service.features.slice(0, 3).map((feature, idx) => (
                        <small key={idx} className="badge bg-light text-dark me-1 mb-1">
                          {feature}
                        </small>
                      ))}
                    </div>
                    <button
                      onClick={() => handleBookService(service)}
                      className="btn btn_theme_white btn_md"
                      disabled={!service.available || bookingService[service.id]}
                    >
                      {bookingService[service.id]
                        ? 'Booking...'
                        : service.available
                          ? 'Book Service'
                          : 'Unavailable'
                      }
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {services.length === 0 && !loading && !error && (
              <div className="col-12 text-center py-5">
                <h4>No services available</h4>
                <p>Please check back later for our services.</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Service;
