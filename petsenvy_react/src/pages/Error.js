import React from 'react';
import { Link } from 'react-router-dom';

const Error = () => {
  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>404 Error</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span>Error Page</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Error page Area */}
      <section id="error_main_area" className="section_padding">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 offset-lg-2 col-md-12 col-12">
              <div className="error_inner">
                <img src="/img/common/error.png" alt="img" />
                <h2>Oops! This page seems to have gone for a walk.</h2>
                <p>The page you are looking for might have been removed, had its name changed, or is temporarily unavailable. Let's get you back on track!</p>
                <Link to="/" className="btn btn_theme btn_md">Go back home</Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Error;
