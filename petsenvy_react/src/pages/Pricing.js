import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useModal } from '../contexts/ModalContext';
import { useBooking } from '../contexts/BookingContext';
import { subscriptionServices } from '../services/firebaseServices';
import { initializeDatabase } from '../utils/initializeDatabase';
import { showSuccessToast, showErrorToast, showWarningToast } from '../utils/toast';

const Pricing = () => {
  const { currentUser } = useAuth();
  const { showInfoModal, showWarningModal, showSuccessModal, showConfirmModal } = useModal();
  const { subscribeToPlan, loading: subscriptionLoading } = useBooking();
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [subscribingPlan, setSubscribingPlan] = useState({}); // Track subscription state per plan

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      setLoading(true);
      setError('');

      // Initialize database if needed
      await initializeDatabase();

      // Load subscription plans from Firebase
      const plansData = await subscriptionServices.getAllPlans();
      setPlans(plansData);
    } catch (error) {
      console.error('Error loading plans:', error);
      setError('Failed to load pricing plans. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const handleSubscribe = async (plan) => {
    if (!currentUser) {
      showWarningModal(
        'Login Required',
        'Please log in to subscribe to a plan.',
        () => {
          window.location.href = '/my-account';
        }
      );
      return;
    }

    showConfirmModal(
      'Confirm Subscription',
      `Are you sure you want to subscribe to "${plan.name}" for ${formatPrice(plan.price)}/${plan.billingCycle}?`,
      async () => {
        try {
          setSubscribingPlan(prev => ({ ...prev, [plan.id]: true }));
          await subscribeToPlan(plan);
          showSuccessModal(
            'Subscription Successful!',
            `You have successfully subscribed to "${plan.name}". You can view and manage your subscriptions in your dashboard.`
          );
        } catch (error) {
          console.error('Error subscribing to plan:', error);
          showErrorToast('Failed to subscribe to plan. Please try again.');
        } finally {
          setSubscribingPlan(prev => ({ ...prev, [plan.id]: false }));
        }
      }
    );
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>Our Pricing</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span> Our Pricing</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our pricing Area */}
      <section id="our_pricing_area" className="section_padding">
        <div className="container">
          {/* Section Heading */}
          <div className="row">
            <div className="col-lg-6 offset-lg-3">
              <div className="section_heading">
                <h2>Our Pricing Plans</h2>
                <p>Choose a care plan that suits your pet's lifestyle—trusted, affordable, and packed with love and professional support.</p>
              </div>
            </div>
          </div>
          {/* Inner Content */}
          <div className="row">
            {error && (
              <div className="col-12">
                <div className="alert alert-danger text-center">
                  <p>{error}</p>
                  <button className="btn btn_theme" onClick={loadPlans}>
                    Try Again
                  </button>
                </div>
              </div>
            )}

            {plans.map((plan) => (
              <div key={plan.id} className="col-lg-4 col-md-6 col-sm-12 col-12">
                <div className="our_pricing_wrapper">
                  <div className={`our_pricing_top ${plan.popular ? 'pricing_active' : ''}`}>
                    <h3>{plan.name}</h3>
                    <h2>
                      {formatPrice(plan.price)}
                      <sub>/{plan.billingCycle === 'yearly' ? 'Per year' : 'Per month'}</sub>
                    </h2>
                    <p>{plan.description}</p>
                    {plan.popular && (
                      <div className="popular_badge">
                        <span className="badge bg-warning text-dark">Most Popular</span>
                      </div>
                    )}
                  </div>
                  <div className="our_pricing_bottom">
                    <ul>
                      {plan.features.map((feature, index) => (
                        <li key={index} className="active">
                          <i className="fas fa-check-circle"></i> {feature}
                        </li>
                      ))}
                    </ul>
                    <div className="our_pricing_btn">
                      <button
                        onClick={() => handleSubscribe(plan)}
                        className={`btn ${plan.popular ? 'btn_theme' : 'btn_theme_white'} btn_md`}
                        disabled={!plan.available || subscribingPlan[plan.id]}
                      >
                        {subscribingPlan[plan.id]
                          ? 'Subscribing...'
                          : plan.available
                            ? 'Subscribe Now'
                            : 'Unavailable'
                        }
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {plans.length === 0 && !loading && !error && (
              <div className="col-12 text-center py-5">
                <h4>No pricing plans available</h4>
                <p>Please check back later for our subscription plans.</p>
              </div>
            )}
          </div>
        </div>
      </section>
      
      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Pricing;
