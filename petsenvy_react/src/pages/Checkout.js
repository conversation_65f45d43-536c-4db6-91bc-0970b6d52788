import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { useBooking } from '../contexts/BookingContext';
import { showSuccessToast, showErrorToast } from '../utils/toast';

const Checkout = () => {
  const { cartItems, getCartTotal, clearCart } = useCart();
  const { currentUser } = useAuth();
  const { createOrder } = useBooking();
  const navigate = useNavigate();

  const [paymentMethod, setPaymentMethod] = useState('cashOnDelivery');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: currentUser?.email || '',
    mobile: '',
    streetAddress: '',
    apartment: '',
    country: '',
    city: '',
    postalCode: '',
    orderNotes: ''
  });

  useEffect(() => {
    // Redirect if not logged in or cart is empty
    if (!currentUser) {
      navigate('/my-account');
      return;
    }
    if (cartItems.length === 0) {
      navigate('/cart');
      return;
    }
  }, [currentUser, cartItems, navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const subtotal = getCartTotal();
  const shipping = subtotal > 50 ? 0 : 10;
  const grandTotal = subtotal + shipping;

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!currentUser) {
      setError('Please log in to place an order');
      return;
    }

    if (cartItems.length === 0) {
      setError('Your cart is empty');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Create order data
      const orderData = {
        items: cartItems.map(item => ({
          productId: item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          image: item.image
        })),
        total: grandTotal,
        subtotal: subtotal,
        shipping: shipping,
        shippingAddress: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          mobile: formData.mobile,
          streetAddress: formData.streetAddress,
          apartment: formData.apartment,
          country: formData.country,
          city: formData.city,
          postalCode: formData.postalCode
        },
        paymentMethod: paymentMethod,
        orderNotes: formData.orderNotes
      };

      // Create order using booking context
      const order = await createOrder(orderData);

      // Clear cart after successful order
      await clearCart();

      // Show success message
      showSuccessToast(`Order #${order.id.slice(-6)} placed successfully!`);

      // Redirect to dashboard
      navigate('/dashboard');

    } catch (error) {
      console.error('Error placing order:', error);
      setError('Failed to place order. Please try again.');
      showErrorToast('Failed to place order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>Checkout</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span>Checkout</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Checkout Area */}
      <section id="checkout_main_area" className="section_padding">
        <div className="container">
          <div className="row">
            <div className="col-lg-8">
              <div className="shipping_addres_area_main">
                <div className="shipping_addres_main_form_area">
                  <h2>Billing Details</h2>
                  <div className="shipping_address_form">
                    <form id="checkout-form" onSubmit={handleSubmit}>
                      <div className="row">
                        <div className="col-lg-6">
                          <div className="form-group">
                            <input 
                              type="text" 
                              className="form-control" 
                              placeholder="First name*" 
                              name="firstName"
                              value={formData.firstName}
                              onChange={handleInputChange}
                              required 
                            />
                          </div>
                        </div>
                        <div className="col-lg-6">
                          <div className="form-group">
                            <input 
                              type="text" 
                              className="form-control" 
                              placeholder="Last name*" 
                              name="lastName"
                              value={formData.lastName}
                              onChange={handleInputChange}
                              required 
                            />
                          </div>
                        </div>
                        <div className="col-lg-6">
                          <div className="form-group">
                            <input 
                              type="email" 
                              className="form-control" 
                              placeholder="Email address" 
                              name="email"
                              value={formData.email}
                              onChange={handleInputChange}
                              required 
                            />
                          </div>
                        </div>
                        <div className="col-lg-6">
                          <div className="form-group">
                            <input 
                              type="text" 
                              className="form-control" 
                              placeholder="Mobile number*" 
                              name="mobile"
                              value={formData.mobile}
                              onChange={handleInputChange}
                              required 
                            />
                          </div>
                        </div>
                        <div className="col-lg-12">
                          <div className="form-group">
                            <input 
                              type="text" 
                              className="form-control" 
                              placeholder="Street address*" 
                              name="streetAddress"
                              value={formData.streetAddress}
                              onChange={handleInputChange}
                              required 
                            />
                          </div>
                        </div>
                        <div className="col-lg-6">
                          <div className="form-group">
                            <input 
                              type="text" 
                              className="form-control" 
                              placeholder="Apartment, Suite, etc. (optional)"
                              name="apartment"
                              value={formData.apartment}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>
                        <div className="col-lg-6">
                          <div className="form-group">
                            <select 
                              className="form-select form-control" 
                              name="country"
                              value={formData.country}
                              onChange={handleInputChange}
                              required
                            >
                              <option value="" disabled>Country*</option>
                              <option value="pakistan">Pakistan</option>
                            </select>
                          </div>
                        </div>
                        <div className="col-lg-6">
                          <div className="form-group">
                            <select 
                              className="form-select form-control" 
                              name="city"
                              value={formData.city}
                              onChange={handleInputChange}
                              required
                            >
                              <option value="" disabled>City*</option>
                              <option value="lahore">Lahore</option>
                              <option value="karachi">Karachi</option>
                              <option value="islamabad">Islamabad</option>
                              <option value="rawalpindi">Rawalpindi</option>
                              <option value="faisalabad">Faisalabad</option>
                            </select>
                          </div>
                        </div>
                        <div className="col-lg-6">
                          <div className="form-group">
                            <input 
                              type="text" 
                              className="form-control" 
                              placeholder="Postal code"
                              name="postalCode"
                              value={formData.postalCode}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>
                        <div className="col-lg-12">
                          <div className="form-group">
                            <textarea 
                              rows="5" 
                              className="form-control" 
                              placeholder="Order notes (optional)"
                              name="orderNotes"
                              value={formData.orderNotes}
                              onChange={handleInputChange}
                            ></textarea>
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
                <div className="payment_method_area">
                  <h2>Payment Method</h2>
                  <div className="payment_method_options">
                    <div className="form-check">
                      <input 
                        className="form-check-input" 
                        type="radio" 
                        name="paymentMethod" 
                        id="cashOnDelivery" 
                        checked={paymentMethod === 'cashOnDelivery'}
                        onChange={() => setPaymentMethod('cashOnDelivery')}
                      />
                      <label className="form-check-label" htmlFor="cashOnDelivery">
                        Cash on Delivery
                      </label>
                    </div>
                    <div className="form-check">
                      <input 
                        className="form-check-input" 
                        type="radio" 
                        name="paymentMethod" 
                        id="creditCard"
                        checked={paymentMethod === 'creditCard'}
                        onChange={() => setPaymentMethod('creditCard')}
                      />
                      <label className="form-check-label" htmlFor="creditCard">
                        Credit/Debit Card
                      </label>
                    </div>
                  </div>
                </div>
                <div className="order_button">
                  <button
                    type="submit"
                    form="checkout-form"
                    className="btn btn_theme btn_md"
                    disabled={loading}
                  >
                    {loading ? 'Placing Order...' : 'Place Order'}
                  </button>
                </div>
              </div>
            </div>
            <div className="col-lg-4">
              <div className="cart_area_total_wrapper">
                <h3>Order Summary</h3>
                {error && <div className="alert alert-danger">{error}</div>}
                {cartItems.map((item, index) => (
                  <div key={index} className={`cart_total_item ${index % 2 === 0 ? 'bg_cart_item' : ''}`}>
                    <h5>{item.name} (x{item.quantity}) <span>{formatPrice(item.price * item.quantity)}</span></h5>
                  </div>
                ))}
                <div className="cart_total_item">
                  <h5>Subtotal <span>{formatPrice(subtotal)}</span></h5>
                </div>
                <div className="cart_total_item">
                  <h5>Shipping <span>{shipping === 0 ? 'Free' : formatPrice(shipping)}</span></h5>
                </div>
                <div className="cart_total_area bg_cart_item">
                  <h4>Grand Total: <span>{formatPrice(grandTotal)}</span></h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Checkout;
