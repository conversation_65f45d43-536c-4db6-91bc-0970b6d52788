import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useModal } from '../contexts/ModalContext';
import { useBooking } from '../contexts/BookingContext';
import { showSuccessToast, showErrorToast } from '../utils/toast';

const Dashboard = () => {
  const { currentUser, userProfile, logout } = useAuth();
  const { showConfirmModal } = useModal();
  const {
    bookings,
    subscriptions,
    orders,
    loading,
    cancelBooking,
    cancelSubscription
  } = useBooking();
  const [activeTab, setActiveTab] = useState('overview');

  // Data is automatically loaded by BookingContext

  const handleCancelBooking = (bookingId, serviceName) => {
    showConfirmModal(
      'Cancel Service Booking',
      `Are you sure you want to cancel your booking for "${serviceName}"?`,
      async () => {
        try {
          await cancelBooking(bookingId);
          showSuccessToast('Service booking cancelled successfully');
        } catch (error) {
          console.error('Error cancelling booking:', error);
          showErrorToast('Failed to cancel booking');
        }
      }
    );
  };

  const handleCancelSubscription = (subscriptionId, planName) => {
    showConfirmModal(
      'Cancel Subscription',
      `Are you sure you want to cancel your subscription to "${planName}"?`,
      async () => {
        try {
          await cancelSubscription(subscriptionId);
          showSuccessToast('Subscription cancelled successfully');
        } catch (error) {
          console.error('Error cancelling subscription:', error);
          showErrorToast('Failed to cancel subscription');
        }
      }
    );
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>Dashboard</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span>Dashboard</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Dashboard Area */}
      <section id="dashboard_area" className="section_padding">
        <div className="container">
          <div className="row">
            {/* Sidebar */}
            <div className="col-lg-3">
              <div className="dashboard_sidebar">
                <div className="user_info mb-4">
                  <h4>Welcome, {currentUser?.displayName || 'User'}!</h4>
                  <p className="text-muted">{currentUser?.email}</p>
                </div>
                
                <ul className="nav nav-pills dashboard_menu flex-column">
                  <li className="nav-item">
                    <button 
                      className={`nav-link nav-dashboard ${activeTab === 'overview' ? 'active' : ''}`}
                      onClick={() => setActiveTab('overview')}
                    >
                      Overview
                    </button>
                  </li>
                  <li className="nav-item">
                    <button 
                      className={`nav-link nav-dashboard ${activeTab === 'orders' ? 'active' : ''}`}
                      onClick={() => setActiveTab('orders')}
                    >
                      Orders ({orders.length})
                    </button>
                  </li>
                  <li className="nav-item">
                    <button 
                      className={`nav-link nav-dashboard ${activeTab === 'subscriptions' ? 'active' : ''}`}
                      onClick={() => setActiveTab('subscriptions')}
                    >
                      Subscriptions ({subscriptions.length})
                    </button>
                  </li>
                  <li className="nav-item">
                    <button 
                      className={`nav-link nav-dashboard ${activeTab === 'services' ? 'active' : ''}`}
                      onClick={() => setActiveTab('services')}
                    >
                      Services ({bookings.length})
                    </button>
                  </li>
                  <li className="nav-item">
                    <button 
                      className={`nav-link nav-dashboard ${activeTab === 'profile' ? 'active' : ''}`}
                      onClick={() => setActiveTab('profile')}
                    >
                      Profile
                    </button>
                  </li>
                  <li className="nav-item mt-3">
                    <button className="nav-link text-danger" onClick={handleLogout}>
                      Logout
                    </button>
                  </li>
                </ul>
              </div>
            </div>

            {/* Main Content */}
            <div className="col-lg-9">
              <div className="dashboard_content">
                
                {/* Overview Tab */}
                {activeTab === 'overview' && (
                  <div className="tab-content">
                    <h3>Account Overview</h3>
                    <div className="row mt-4">
                      <div className="col-md-4">
                        <div className="card text-center">
                          <div className="card-body">
                            <h5 className="card-title">{orders.length}</h5>
                            <p className="card-text">Total Orders</p>
                          </div>
                        </div>
                      </div>
                      <div className="col-md-4">
                        <div className="card text-center">
                          <div className="card-body">
                            <h5 className="card-title">{subscriptions.filter(s => s.status === 'active').length}</h5>
                            <p className="card-text">Active Subscriptions</p>
                          </div>
                        </div>
                      </div>
                      <div className="col-md-4">
                        <div className="card text-center">
                          <div className="card-body">
                            <h5 className="card-title">{bookings.length}</h5>
                            <p className="card-text">Service Bookings</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Recent Activity */}
                    <div className="mt-5">
                      <h4>Recent Activity</h4>
                      <div className="list-group">
                        {orders.slice(0, 3).map(order => (
                          <div key={order.id} className="list-group-item">
                            <div className="d-flex w-100 justify-content-between">
                              <h6 className="mb-1">Order #{order.id.slice(-6)}</h6>
                              <small>{formatDate(order.createdAt)}</small>
                            </div>
                            <p className="mb-1">Status: <span className="badge bg-primary">{order.status}</span></p>
                            <small>Total: {formatPrice(order.total)}</small>
                          </div>
                        ))}
                        {orders.length === 0 && (
                          <div className="list-group-item text-center">
                            <p className="mb-0">No recent orders</p>
                            <Link to="/shop" className="btn btn_theme btn-sm mt-2">Start Shopping</Link>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Orders Tab */}
                {activeTab === 'orders' && (
                  <div className="tab-content">
                    <h3>My Orders</h3>
                    <div className="table-responsive mt-4">
                      <table className="table table-striped">
                        <thead>
                          <tr>
                            <th>Order ID</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Total</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {orders.map(order => (
                            <tr key={order.id}>
                              <td>#{order.id.slice(-6)}</td>
                              <td>{formatDate(order.createdAt)}</td>
                              <td>
                                <span className={`badge ${
                                  order.status === 'completed' ? 'bg-success' :
                                  order.status === 'pending' ? 'bg-warning' :
                                  order.status === 'cancelled' ? 'bg-danger' : 'bg-primary'
                                }`}>
                                  {order.status}
                                </span>
                              </td>
                              <td>{formatPrice(order.total)}</td>
                              <td>
                                <button className="btn btn-sm btn-outline-primary">View Details</button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {orders.length === 0 && (
                        <div className="text-center py-4">
                          <p>No orders found</p>
                          <Link to="/shop" className="btn btn_theme">Start Shopping</Link>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Subscriptions Tab */}
                {activeTab === 'subscriptions' && (
                  <div className="tab-content">
                    <h3>My Subscriptions</h3>
                    <div className="row mt-4">
                      {subscriptions.map(subscription => (
                        <div key={subscription.id} className="col-md-6 mb-4">
                          <div className="card">
                            <div className="card-body">
                              <h5 className="card-title">{subscription.planName}</h5>
                              <p className="card-text">
                                <strong>Price:</strong> ${subscription.planPrice}/{subscription.planDuration}
                              </p>
                              <p className="card-text">
                                Status: <span className={`badge ${
                                  subscription.status === 'active' ? 'bg-success' :
                                  subscription.status === 'cancelled' ? 'bg-danger' : 'bg-secondary'
                                }`}>
                                  {subscription.status}
                                </span>
                              </p>
                              <p className="card-text">
                                <small className="text-muted">
                                  Started: {formatDate(subscription.createdAt)}
                                  {subscription.nextBillingDate && subscription.status === 'active' && (
                                    <><br />Next billing: {formatDate(subscription.nextBillingDate)}</>
                                  )}
                                </small>
                              </p>
                              {subscription.status === 'active' && (
                                <button
                                  className="btn btn-outline-danger btn-sm"
                                  onClick={() => handleCancelSubscription(subscription.id, subscription.planName)}
                                >
                                  Cancel Subscription
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                      {subscriptions.length === 0 && (
                        <div className="col-12 text-center">
                          <p>No active subscriptions</p>
                          <Link to="/pricing" className="btn btn_theme">View Plans</Link>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Services Tab */}
                {activeTab === 'services' && (
                  <div className="tab-content">
                    <h3>My Service Bookings</h3>
                    <div className="table-responsive mt-4">
                      <table className="table table-striped">
                        <thead>
                          <tr>
                            <th>Service</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {bookings.map(booking => (
                            <tr key={booking.id}>
                              <td>{booking.serviceName}</td>
                              <td>{formatDate(booking.createdAt)}</td>
                              <td>
                                <span className={`badge ${
                                  booking.status === 'confirmed' ? 'bg-success' :
                                  booking.status === 'pending' ? 'bg-warning' :
                                  booking.status === 'cancelled' ? 'bg-danger' : 'bg-primary'
                                }`}>
                                  {booking.status}
                                </span>
                              </td>
                              <td>
                                {booking.status !== 'cancelled' && (
                                  <button
                                    className="btn btn-sm btn-outline-danger"
                                    onClick={() => handleCancelBooking(booking.id, booking.serviceName)}
                                  >
                                    Cancel
                                  </button>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {bookings.length === 0 && (
                        <div className="text-center py-4">
                          <p>No service bookings found</p>
                          <Link to="/service" className="btn btn_theme">Book a Service</Link>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Profile Tab */}
                {activeTab === 'profile' && (
                  <div className="tab-content">
                    <h3>Profile Information</h3>
                    <div className="row mt-4">
                      <div className="col-md-8">
                        <div className="card">
                          <div className="card-body">
                            <form>
                              <div className="mb-3">
                                <label className="form-label">Display Name</label>
                                <input 
                                  type="text" 
                                  className="form-control" 
                                  value={currentUser?.displayName || ''} 
                                  readOnly 
                                />
                              </div>
                              <div className="mb-3">
                                <label className="form-label">Email</label>
                                <input 
                                  type="email" 
                                  className="form-control" 
                                  value={currentUser?.email || ''} 
                                  readOnly 
                                />
                              </div>
                              <div className="mb-3">
                                <label className="form-label">Member Since</label>
                                <input 
                                  type="text" 
                                  className="form-control" 
                                  value={formatDate(userProfile?.createdAt)} 
                                  readOnly 
                                />
                              </div>
                              <button type="button" className="btn btn_theme">Update Profile</button>
                            </form>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Dashboard;
