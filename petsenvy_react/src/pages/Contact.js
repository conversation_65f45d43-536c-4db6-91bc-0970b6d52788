import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import emailjs from '@emailjs/browser';
import { showSuccessToast, showErrorToast } from '../utils/toast';
import { EMAILJS_CONFIG } from '../config/emailjs';

const Contact = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    service: '',
    phone: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // EmailJS configuration from config file

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic validation
    if (!formData.fullName || !formData.email || !formData.message) {
      showErrorToast('Please fill in all required fields.');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      showErrorToast('Please enter a valid email address.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare template parameters for EmailJS
      const templateParams = {
        from_name: formData.fullName,
        from_email: formData.email,
        phone: formData.phone,
        service: formData.service || 'General Inquiry',
        message: formData.message,
        to_email: EMAILJS_CONFIG.TO_EMAIL
      };

      // Send email using EmailJS
      await emailjs.send(
        EMAILJS_CONFIG.SERVICE_ID,
        EMAILJS_CONFIG.TEMPLATE_ID,
        templateParams,
        EMAILJS_CONFIG.PUBLIC_KEY
      );

      showSuccessToast('Message sent successfully! We\'ll get back to you soon.');

      // Reset form
      setFormData({
        fullName: '',
        email: '',
        service: '',
        phone: '',
        message: ''
      });

    } catch (error) {
      console.error('Error sending email:', error);
      showErrorToast('Failed to send message. Please try again or contact us directly.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>Contact Us</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span>Contact Us</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact us page Area */}
      <section id="contact_main_card_area" className="section_padding">
        <div className="container">
          <div className="row">
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="contact_card_item">
                <i className="fas fa-map-marker-alt"></i>
                <h3>Address</h3>
                <p>32, E milad Park, Johar Town, Lahore.</p>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="contact_card_item">
                <i className="fas fa-envelope"></i>
                <h3>Email</h3>
                <ul>
                  <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                  <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                </ul>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="contact_card_item">
                <i className="fas fa-phone-alt"></i>
                <h3>Phone number</h3>
                <ul>
                  <li><a href="tel:+923131591819">+92 313 1591819</a></li>
                  <li><a href="tel:+923114587959">+92 311 4587959</a></li>
                </ul>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="contact_card_item">
                <i className="fas fa-clock"></i>
                <h3>Open hours</h3>
                <ul>
                  <li>Sat-Fri: 8am-10pm</li>
                  <li>Sunday: Closed</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact form Area */}
      <section id="contact_main_form_area" className="section_padding_bottom">
        <div className="container">
          <div className="row">
            <div className="col-lg-6">
              <div className="contact_form_left_side">
                <div className="contact_form_hrading">
                  <h2>Get In Touch</h2>
                  <p>
                    Have a question or need to schedule a service? Fill out the form below, and our team will get back to you as soon as possible.
                  </p>
                </div>
                <div className="contact_form_main_area">
                  <form onSubmit={handleSubmit} id="contact_form">
                    <div className="row">
                      <div className="col-lg-6">
                        <div className="form-group">
                          <input
                            type="text"
                            name="fullName"
                            className="form-control"
                            placeholder="Enter full name"
                            value={formData.fullName}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6">
                        <div className="form-group">
                          <input
                            type="email"
                            name="email"
                            className="form-control"
                            placeholder="Enter email address"
                            value={formData.email}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6">
                        <div className="form-group">
                          <select
                            name="service"
                            className="form-select form-control"
                            value={formData.service}
                            onChange={handleInputChange}
                          >
                            <option value="">Select service</option>
                            <option value="Grooming">Grooming</option>
                            <option value="Wellness Check">Wellness Check</option>
                            <option value="Boarding">Boarding</option>
                            <option value="Adoption Inquiry">Adoption Inquiry</option>
                            <option value="General Question">General Question</option>
                          </select>
                        </div>
                      </div>
                      <div className="col-lg-6">
                        <div className="form-group">
                          <input
                            type="tel"
                            name="phone"
                            className="form-control"
                            placeholder="Your Phone Number"
                            value={formData.phone}
                            onChange={handleInputChange}
                          />
                        </div>
                      </div>
                      <div className="col-lg-12">
                        <div className="form-group">
                          <textarea
                            rows="5"
                            name="message"
                            placeholder="Write your message"
                            className="form-control"
                            value={formData.message}
                            onChange={handleInputChange}
                            required
                          ></textarea>
                        </div>
                      </div>
                      <div className="col-lg-12">
                        <div className="submit_btn">
                          <button
                            type="submit"
                            className="btn btn_theme btn_md"
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? 'Sending...' : 'Send Message'}
                          </button>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="contact_map_area">
                <iframe 
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3403.208425139224!2d74.26946007469792!3d31.46356714902264!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x391903d8e5359283%3A0x6e7374521462470e!2sE-Block%20Milad%20Park!5e0!3m2!1sen!2s!4v1678886543210!5m2!1sen!2s"
                  title="PetsEnvy Location"
                ></iframe>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Contact;
