import React from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { useModal } from '../contexts/ModalContext';
import { showSuccessToast, showErrorToast } from '../utils/toast';

const Cart = () => {
  const { cartItems, updateQuantity, removeFromCart, clearCart, getCartTotal, loading } = useCart();
  const { currentUser } = useAuth();
  const { showConfirmModal } = useModal();

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const subtotal = getCartTotal();
  const shipping = subtotal > 50 ? 0 : 10; // Free shipping over $50
  const grandTotal = subtotal + shipping;

  const handleRemoveItem = (item) => {
    showConfirmModal(
      'Remove Item',
      `Are you sure you want to remove "${item.name}" from your cart?`,
      async () => {
        try {
          await removeFromCart(item.id);
          showSuccessToast('Item removed from cart');
        } catch (error) {
          showErrorToast('Failed to remove item from cart');
        }
      }
    );
  };

  const handleClearCart = () => {
    showConfirmModal(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      async () => {
        try {
          await clearCart();
          showSuccessToast('Cart cleared successfully');
        } catch (error) {
          showErrorToast('Failed to clear cart');
        }
      }
    );
  };

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>Shopping Cart</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span>Cart</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Cart Area */}
      <section id="cart_main_area" className="section_padding">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="cart_groomers_area_wrapper">
                <div className="cart_tabel_area table-responsive">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>Image</th>
                        <th>Product name</th>
                        <th>Unit price</th>
                        <th>Quantity</th>
                        <th>Total</th>
                        <th></th>
                      </tr>
                    </thead>
                    <tbody>
                      {cartItems.length === 0 ? (
                        <tr>
                          <td colSpan="6" className="text-center py-5">
                            <h4>Your cart is empty</h4>
                            <p>Add some products to get started!</p>
                            <Link to="/shop" className="btn btn_theme">
                              Continue Shopping
                            </Link>
                          </td>
                        </tr>
                      ) : (
                        cartItems.map((item) => (
                          <tr key={item.id}>
                            <td>
                              <img src={item.image} alt={item.name} />
                            </td>
                            <td>{item.name}</td>
                            <td>{formatPrice(item.price)}</td>
                            <td>
                              <form action="#!" className="product_count_form_two">
                                <div className="product_count_one">
                                  <div className="plus-minus-input">
                                    <div className="input-group-button">
                                      <button
                                        type="button"
                                        className="button"
                                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                        disabled={loading}
                                      >
                                        <i className="fa fa-minus" aria-hidden="true"></i>
                                      </button>
                                    </div>
                                    <input
                                      className="form-control"
                                      type="number"
                                      name="quantity"
                                      value={item.quantity}
                                      onChange={(e) => updateQuantity(item.id, parseInt(e.target.value) || 1)}
                                      disabled={loading}
                                    />
                                    <div className="input-group-button">
                                      <button
                                        type="button"
                                        className="button"
                                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                        disabled={loading}
                                      >
                                        <i className="fa fa-plus" aria-hidden="true"></i>
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </form>
                            </td>
                            <td>{formatPrice(item.price * item.quantity)}</td>
                            <td>
                              <i
                                className="fas fa-trash"
                                style={{ cursor: 'pointer' }}
                                onClick={() => handleRemoveItem(item)}
                              ></i>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                  {cartItems.length > 0 && (
                    <div className="cart_tabel_bottom">
                      <div className="cart_submit_btn">
                        <Link to="/shop" className="btn btn_theme btn_md">Continue Shopping</Link>
                        <button
                          onClick={handleClearCart}
                          className="btn btn-outline-danger btn_md ms-2"
                          disabled={loading}
                        >
                          Clear Cart
                        </button>
                      </div>
                      <div className="cart_right_side">
                        <form action="#!" id="subscribe_form1">
                          <div className="input-group">
                            <input type="text" className="form-control" placeholder="Your coupon code" />
                            <button className="btn btn_theme btn_md" type="submit">Apply Coupon</button>
                          </div>
                        </form>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          {cartItems.length > 0 && (
            <div className="cart_bottom_area">
              <div className="row">
                <div className="col-lg-4 offset-lg-8">
                  <div className="cart_area_total_wrapper">
                    <div className="cart_total_item">
                      <h5>Subtotal <span>{formatPrice(subtotal)}</span></h5>
                    </div>
                    <div className="cart_total_item">
                      <h5>Shipping <span>{shipping === 0 ? 'Free' : formatPrice(shipping)}</span></h5>
                      {shipping === 0 && <small className="text-success">Free shipping on orders over $50!</small>}
                    </div>
                    <div className="cart_total_area bg_cart_item">
                      <h4>Grand Total: <span>{formatPrice(grandTotal)}</span></h4>
                    </div>
                  </div>
                  <div className="cart_proce_btn">
                    {currentUser ? (
                      <Link to="/checkout" className="btn btn_theme btn_md">Proceed to Checkout</Link>
                    ) : (
                      <div>
                        <p className="text-center mb-3">Please log in to proceed with checkout</p>
                        <Link to="/my-account" className="btn btn_theme btn_md w-100">Login to Checkout</Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Cart;
