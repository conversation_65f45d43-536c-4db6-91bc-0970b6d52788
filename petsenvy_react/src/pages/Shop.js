import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { useModal } from '../contexts/ModalContext';
import { productServices } from '../services/firebaseServices';
import { initializeDatabase } from '../utils/initializeDatabase';
import { showSuccessToast, showErrorToast } from '../utils/toast';
import '../components/shop-components.css';

const Shop = () => {
  const { addToCart, loading: cartLoading } = useCart();
  const { currentUser } = useAuth();
  const { showWarningModal } = useModal();
  const [priceRange, setPriceRange] = useState([0, 200]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [addingToCart, setAddingToCart] = useState({}); // Track loading state per product

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      setError('');

      // Initialize database if needed
      await initializeDatabase();

      // Load products from Firebase
      const productsData = await productServices.getAllProducts();
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading products:', error);
      setError('Failed to load products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = async (product) => {
    if (!currentUser) {
      showWarningModal(
        'Login Required',
        'Please log in to add items to your cart.',
        () => {
          // Redirect to login page
          window.location.href = '/my-account';
        }
      );
      return;
    }

    try {
      setAddingToCart(prev => ({ ...prev, [product.id]: true }));
      await addToCart(product, 1);
      showSuccessToast(`${product.name} added to cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      showErrorToast('Failed to add product to cart. Please try again.');
    } finally {
      setAddingToCart(prev => ({ ...prev, [product.id]: false }));
    }
  };


  const handleCategoryChange = (category) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const handlePriceChange = (e) => {
    const value = parseInt(e.target.value);
    setPriceRange([0, value]);
  };

  const filteredProducts = products.filter(product => {
    const price = product.price;
    const priceInRange = price >= priceRange[0] && price <= priceRange[1];
    const categoryMatch = selectedCategories.length === 0 || selectedCategories.includes(product.category.toLowerCase());
    return priceInRange && categoryMatch;
  });

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<i key={i} className="fas fa-star"></i>);
    }

    if (hasHalfStar) {
      stars.push(<i key="half" className="fas fa-star-half-alt"></i>);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<i key={`empty-${i}`} className="far fa-star"></i>);
    }

    return stars;
  };

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>Shop</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span> Shop</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Shop */}
      <section id="our_shop_main" className="section_padding">
        <div className="container">
          <div className="row">
            <div className="col-lg-9">
              <div className="shop_main_area_wrapper">
                <div className="shop_heading_sort_area">
                  <div className="shop_main_area_heading">
                    <h3>Showing 1-{filteredProducts.length} of {products.length} items</h3>
                  </div>
                  <div className="sort_main_area">
                    <i className="fas fa-th-large"></i>
                    <select className="form-select">
                      <option selected>Default sorting</option>
                      <option value="1">Sort by popularity</option>
                      <option value="2">Sort by newness</option>
                      <option value="3">Sort by price: low to high</option>
                      <option value="4">Sort by price: high to low</option>
                    </select>
                  </div>
                </div>
                <div className="shop_item_wrapper">
                  {loading ? (
                    <div className="text-center py-5">
                      <div className="spinner-border" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                      <p className="mt-3">Loading products...</p>
                    </div>
                  ) : error ? (
                    <div className="alert alert-danger text-center">
                      <p>{error}</p>
                      <button className="btn btn_theme" onClick={loadProducts}>
                        Try Again
                      </button>
                    </div>
                  ) : (
                    <div className="row">
                      {filteredProducts.map((product) => (
                        <div key={product.id} className="col-lg-4 col-md-6 col-sm-12 col-12">
                          <div className="shop_main_item">
                            <div className="shop_item_img">
                              <Link to={`/shop-details/${product.id}`}>
                                <img src={product.image} alt={product.name} />
                              </Link>
                              <span className={`shop_badge ${product.inStock ? 'in_stock' : 'out_of_stock'}`}>
                                {product.inStock ? 'In Stock' : 'Out of Stock'}
                              </span>
                            </div>
                            <div className="shop_item_content">
                              <h3>
                                <Link to={`/shop-details/${product.id}`}>{product.name}</Link>
                              </h3>
                              <div className="shop_item_price">
                                <h5>{formatPrice(product.price)}</h5>
                              </div>
                              <div className="shop_item_rating">
                                {renderStars(product.rating)}
                                <span>({product.reviews})</span>
                              </div>
                              <div className="shop_quent_wrapper">
                                <button
                                  onClick={() => handleAddToCart(product)}
                                  className="btn btn_theme btn_sm"
                                  disabled={!product.inStock || addingToCart[product.id]}
                                >
                                  {addingToCart[product.id] ? 'Adding...' : 'Add to cart'}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                      {filteredProducts.length === 0 && !loading && (
                        <div className="col-12 text-center py-5">
                          <h4>No products found</h4>
                          <p>Try adjusting your filters or browse all products.</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="col-lg-3">
              <div className="sidebar_boxed_wrapper">
                <div className="sidebar_common_heading">
                  <h3>Filter by price</h3>
                </div>
                <div className="filter-price">
                  <div className="price-slider-wrapper">
                    <input
                      type="range"
                      min="0"
                      max="200"
                      value={priceRange[1]}
                      onChange={handlePriceChange}
                      className="price-slider"
                    />
                    <div className="price-range-display">
                      <span>{formatPrice(priceRange[0])} - {formatPrice(priceRange[1])}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="sidebar_boxed_wrapper">
                <div className="sidebar_common_heading">
                  <h3>Category</h3>
                </div>
                <div className="shop_sidebar_category">
                  <div className="sidebar_form_checkboxed">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        value="food"
                        id="cat1"
                        checked={selectedCategories.includes('food')}
                        onChange={() => handleCategoryChange('food')}
                      />
                      <label className="form-check-label" htmlFor="cat1">
                        <span>Food & Treats</span>
                        <span className="shop_cate_conter">
                          {products.filter(p => p.category.toLowerCase() === 'food').length}
                        </span>
                      </label>
                    </div>
                  </div>
                  <div className="sidebar_form_checkboxed">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        value="toys"
                        id="cat2"
                        checked={selectedCategories.includes('toys')}
                        onChange={() => handleCategoryChange('toys')}
                      />
                      <label className="form-check-label" htmlFor="cat2">
                        <span>Toys & Chews</span>
                        <span className="shop_cate_conter">14</span>
                      </label>
                    </div>
                  </div>
                  <div className="sidebar_form_checkboxed">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        value="beds"
                        id="cat3"
                        checked={selectedCategories.includes('beds')}
                        onChange={() => handleCategoryChange('beds')}
                      />
                      <label className="form-check-label" htmlFor="cat3">
                        <span>Beds & Baskets</span>
                        <span className="shop_cate_conter">84</span>
                      </label>
                    </div>
                  </div>
                  <div className="sidebar_form_checkboxed">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        value="health"
                        id="cat4"
                        checked={selectedCategories.includes('health')}
                        onChange={() => handleCategoryChange('health')}
                      />
                      <label className="form-check-label" htmlFor="cat4">
                        <span>Health & Wellness</span>
                        <span className="shop_cate_conter">08</span>
                      </label>
                    </div>
                  </div>
                  <div className="sidebar_form_checkboxed">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        value="leashes"
                        id="cat5"
                        checked={selectedCategories.includes('leashes')}
                        onChange={() => handleCategoryChange('leashes')}
                      />
                      <label className="form-check-label" htmlFor="cat5">
                        <span>Leashes & Collars</span>
                        <span className="shop_cate_conter">06</span>
                      </label>
                    </div>
                  </div>
                  <div className="sidebar_form_checkboxed">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        value="grooming"
                        id="cat6"
                        checked={selectedCategories.includes('grooming')}
                        onChange={() => handleCategoryChange('grooming')}
                      />
                      <label className="form-check-label" htmlFor="cat6">
                        <span>Grooming Supplies</span>
                        <span className="shop_cate_conter">15</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div className="sidebar_boxed_wrapper">
                <div className="sidebar_add_img">
                  <a href="#!">
                    <img src="/img/shop/ad_banner.png" alt="img" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Shop;
