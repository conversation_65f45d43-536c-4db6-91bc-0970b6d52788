import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { useModal } from '../contexts/ModalContext';
import { productServices } from '../services/firebaseServices';
import { showSuccessToast, showErrorToast } from '../utils/toast';

const ShopDetails = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const { currentUser } = useAuth();
  const { showWarningModal } = useModal();

  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [selectedColor, setSelectedColor] = useState('red');
  const [activeTab, setActiveTab] = useState('description');
  const [addingToCart, setAddingToCart] = useState(false);

  // Load product data
  useEffect(() => {
    const loadProduct = async () => {
      if (!productId) {
        setError('Product ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError('');
        const productData = await productServices.getProductById(productId);
        setProduct(productData);
      } catch (error) {
        console.error('Error loading product:', error);
        setError('Product not found');
      } finally {
        setLoading(false);
      }
    };

    loadProduct();
  }, [productId]);

  useEffect(() => {
    // Initialize Slick slider for shop details images
    if (!product || !window.$ || !window.$.fn.slick) return;

    // Small delay to ensure DOM is ready
    const timer = setTimeout(() => {
        try {
          // Check if sliders are already initialized and destroy them
          if (window.$('.slider-for').hasClass('slick-initialized')) {
            window.$('.slider-for').slick('unslick');
          }
          if (window.$('.slider-nav').hasClass('slick-initialized')) {
            window.$('.slider-nav').slick('unslick');
          }

          // Initialize main image slider
          window.$('.slider-for').slick({
            infinite: true,
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: false,
            fade: true,
            asNavFor: '.slider-nav'
          });

          // Initialize thumbnail navigation slider
          window.$('.slider-nav').slick({
            infinite: true,
            slidesToShow: 4,
            slidesToScroll: 1,
            asNavFor: '.slider-for',
            dots: false,
            focusOnSelect: true,
            responsive: [
              {
                breakpoint: 768,
                settings: {
                  slidesToShow: 3
                }
              },
              {
                breakpoint: 480,
                settings: {
                  slidesToShow: 2
                }
              }
            ]
          });
        } catch (error) {
          console.log('Slick slider initialization error:', error);
        }
    }, 100);

    // Cleanup function
    return () => {
      clearTimeout(timer);
      if (window.$ && window.$.fn.slick) {
        try {
          if (window.$('.slider-for').hasClass('slick-initialized')) {
            window.$('.slider-for').slick('unslick');
          }
          if (window.$('.slider-nav').hasClass('slick-initialized')) {
            window.$('.slider-nav').slick('unslick');
          }
        } catch (e) {
          // Ignore errors if sliders are already destroyed
        }
      }
    };
  }, [product]);

  const updateQuantity = (newQuantity) => {
    if (newQuantity < 1) return;
    setQuantity(newQuantity);
  };

  const handleAddToCart = async () => {
    if (!currentUser) {
      showWarningModal(
        'Login Required',
        'Please log in to add items to your cart.',
        () => {
          navigate('/my-account');
        }
      );
      return;
    }

    if (!product) return;

    try {
      setAddingToCart(true);
      await addToCart(product, quantity);
      showSuccessToast(`${product.name} added to cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      showErrorToast('Failed to add item to cart. Please try again.');
    } finally {
      setAddingToCart(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<i key={i} className="fas fa-star"></i>);
    }

    if (hasHalfStar) {
      stars.push(<i key="half" className="fas fa-star-half-alt"></i>);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<i key={`empty-${i}`} className="far fa-star"></i>);
    }

    return stars;
  };

  if (loading) {
    return (
      <div className="container section_padding">
        <div className="text-center py-5">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading product details...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container section_padding">
        <div className="text-center py-5">
          <h4>Product Not Found</h4>
          <p>{error || 'The requested product could not be found.'}</p>
          <Link to="/shop" className="btn btn_theme btn_md">
            Back to Shop
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>{product.name}</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span><Link to="/shop">Shop</Link></li>
                  <li><span>/</span>{product.name}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Shop Details */}
      <section id="shop_details_area" className="section_padding">
        <div className="container">
          <div className="row">
            <div className="col-lg-4">
              <div className="shop_details_slider_wrapper">
                <div className="slider slider-for">
                  <div>
                    <img src={product.image} alt={product.name} />
                  </div>
                  {product.additionalImages && product.additionalImages.map((img, index) => (
                    <div key={index}>
                      <img src={img} alt={`${product.name} ${index + 1}`} />
                    </div>
                  ))}
                </div>
                <div className="slider slider-nav">
                  <div>
                    <img src={product.image} alt={product.name} />
                  </div>
                  {product.additionalImages && product.additionalImages.map((img, index) => (
                    <div key={index}>
                      <img src={img} alt={`${product.name} ${index + 1}`} />
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="col-lg-8">
              <div className="shop_details_wrapper">
                <div className="shop_details_top_content">
                  <h2>{product.name}</h2>
                  <div className="reviews_rating">
                    {renderStars(product.rating || 4.5)}
                    <span>({product.rating || 4.5}) {product.reviewCount || 0} Reviews</span>
                  </div>
                  <h3>
                    {formatPrice(product.price)}
                    {product.originalPrice && product.originalPrice > product.price && (
                      <del> {formatPrice(product.originalPrice)}</del>
                    )}
                  </h3>
                  <p>{product.description}</p>
                  {!product.inStock && (
                    <div className="alert alert-warning">
                      <i className="fas fa-exclamation-triangle"></i> This item is currently out of stock.
                    </div>
                  )}
                </div>

                <div className="variable-single-item">
                  <span>Color</span>
                  <div className="product-variable-color">
                    <label htmlFor="modal-product-color-red">
                      <input 
                        name="modal-product-color" 
                        id="modal-product-color-red" 
                        className="color-select"
                        type="radio" 
                        checked={selectedColor === 'red'}
                        onChange={() => setSelectedColor('red')}
                      />
                      <span className="product-color-red"></span>
                    </label>
                    <label htmlFor="modal-product-color-green">
                      <input 
                        name="modal-product-color" 
                        id="modal-product-color-green"
                        className="color-select" 
                        type="radio"
                        checked={selectedColor === 'green'}
                        onChange={() => setSelectedColor('green')}
                      />
                      <span className="product-color-green"></span>
                    </label>
                    <label htmlFor="modal-product-color-blue">
                      <input 
                        name="modal-product-color" 
                        id="modal-product-color-blue" 
                        className="color-select"
                        type="radio"
                        checked={selectedColor === 'blue'}
                        onChange={() => setSelectedColor('blue')}
                      />
                      <span className="product-color-blue"></span>
                    </label>
                  </div>
                </div>
                <div className="product_count_wrapper">
                  <form action="#!" className="product_count_form_two">
                    <div className="product_count_one">
                      <div className="plus-minus-input">
                        <div className="input-group-button">
                          <button 
                            type="button" 
                            className="button"
                            onClick={() => updateQuantity(quantity - 1)}
                          >
                            <i className="fa fa-minus" aria-hidden="true"></i>
                          </button>
                        </div>
                        <input 
                          className="form-control" 
                          type="number" 
                          name="quantity" 
                          value={quantity}
                          onChange={(e) => updateQuantity(parseInt(e.target.value) || 1)}
                        />
                        <div className="input-group-button">
                          <button 
                            type="button" 
                            className="button"
                            onClick={() => updateQuantity(quantity + 1)}
                          >
                            <i className="fa fa-plus" aria-hidden="true"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
                <div className="shop_details_cart_submit_wrapper">
                  <div className="product_cart_btn">
                    <button
                      onClick={handleAddToCart}
                      className="btn btn_theme btn_md"
                      disabled={!product.inStock || addingToCart}
                    >
                      {addingToCart ? 'Adding...' : 'Add to Cart'}
                    </button>
                  </div>
                  <div className="product_wishlist_btn">
                    <a href="#!"><i className="far fa-heart"></i> Add to Wishlist</a>
                  </div>
                </div>
                <div className="product_tags_area">
                  <p><span>Tags:</span> Leashes, Walking, Pet Gear, Accessories</p>
                </div>
              </div>
            </div>
          </div>
          <div className="row">
            <div className="col-lg-12">
              <div className="shop_details_tabs_wrapper">
                <div className="shop_details_tab_nav">
                  <nav>
                    <div className="nav nav-tabs" id="nav-tab" role="tablist">
                      <button 
                        className={`nav-link ${activeTab === 'description' ? 'active' : ''}`}
                        onClick={() => setActiveTab('description')}
                        type="button"
                      >
                        Description
                      </button>
                      <button 
                        className={`nav-link ${activeTab === 'additional' ? 'active' : ''}`}
                        onClick={() => setActiveTab('additional')}
                        type="button"
                      >
                        Additional Info
                      </button>
                      <button 
                        className={`nav-link ${activeTab === 'reviews' ? 'active' : ''}`}
                        onClick={() => setActiveTab('reviews')}
                        type="button"
                      >
                        Reviews
                      </button>
                    </div>
                  </nav>
                </div>
                <div className="tab-content" id="nav-tabContent">
                  {activeTab === 'description' && (
                    <div className="tab-pane fade show active">
                      <div className="descriptions_shop_details_wrapper">
                        <div className="shop_details_descriptions_item">
                          <h5>Product Features:</h5>
                          <p>
                            This retractable leash is perfect for daily walks, giving your dog the freedom to explore while keeping you in control. The tangle-free design ensures a smooth experience, and the reflective stitching provides added visibility during night-time walks.
                          </p>
                          <ul>
                            <li><i className="fas fa-check"></i> 5-meter heavy-duty nylon cord.</li>
                            <li><i className="fas fa-check"></i> Comfortable, non-slip ergonomic handle.</li>
                            <li><i className="fas fa-check"></i> Quick-lock and release button for safety.</li>
                            <li><i className="fas fa-check"></i> Suitable for small to medium-sized dogs up to 20kg.</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {activeTab === 'additional' && (
                    <div className="tab-pane fade show active">
                      <div className="additional_info_tabel_wrapper">
                        <table className="table">
                          <tbody>
                            <tr>
                              <td>Weight</td>
                              <td>0.3 kg</td>
                            </tr>
                            <tr>
                              <td>Dimensions</td>
                              <td>15 x 10 x 4 cm</td>
                            </tr>
                            <tr>
                              <td>Material</td>
                              <td>ABS Plastic, Nylon Cord</td>
                            </tr>
                            <tr>
                              <td>Color</td>
                              <td>Red, Green, Blue</td>
                            </tr>
                            <tr>
                              <td>Warranty</td>
                              <td>1-year limited warranty</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                  
                  {activeTab === 'reviews' && (
                    <div className="tab-pane fade show active">
                      <div className="product_review_tabs">
                        <h3>2 Reviews</h3>
                        <div className="product_review_details_item">
                          <div className="product_review_item_img">
                            <img src="/img/common/review-1.png" alt="img" />
                          </div>
                          <div className="product_review_item_content">
                            <h4>Hassan Ali</h4>
                            <p>Great leash! It's very sturdy and the lock mechanism works perfectly. My dog loves the extra freedom on our walks.</p>
                          </div>
                        </div>
                        <div className="product_review_details_item">
                          <div className="product_review_item_img">
                            <img src="/img/common/review-2.png" alt="img" />
                          </div>
                          <div className="product_review_item_content">
                            <h4>Sara Ahmed</h4>
                            <p>Excellent quality and very durable. The handle is comfortable even during long walks. Highly recommended!</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ShopDetails;
