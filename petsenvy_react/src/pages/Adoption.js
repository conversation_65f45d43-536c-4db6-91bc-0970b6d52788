import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';

const Adoption = () => {
  const [activeTab, setActiveTab] = useState('all');

  const pets = [
    {
      id: 1,
      name: "<PERSON>",
      image: "/img/adoption/adoption-4.png",
      gender: "Male",
      age: "3 years",
      breed: "Labrador Mix",
      behavior: "Playful & Loyal",
      type: "dog"
    },
    {
      id: 2,
      name: "<PERSON>",
      image: "/img/adoption/adoption-1.png",
      gender: "Female",
      age: "2 years",
      breed: "Persian",
      behavior: "<PERSON><PERSON> & <PERSON>udd<PERSON>",
      type: "cat"
    },
    {
      id: 3,
      name: "<PERSON>",
      image: "/img/adoption/adoption-2.png",
      gender: "Female",
      age: "1 year",
      breed: "Beagle",
      behavior: "Energetic",
      type: "dog"
    },
    {
      id: 4,
      name: "<PERSON><PERSON>",
      image: "/img/adoption/adoption-3.png",
      gender: "Male",
      age: "4 years",
      breed: "<PERSON><PERSON>",
      behavior: "Independent",
      type: "cat"
    },
    {
      id: 5,
      name: "<PERSON>",
      image: "/img/adoption/adoption-5.png",
      gender: "Male",
      age: "2 years",
      breed: "Golden Retriever",
      behavior: "Friendly & Active",
      type: "dog"
    },
    {
      id: 6,
      name: "Luna",
      image: "/img/adoption/adoption-6.png",
      gender: "Female",
      age: "1 year",
      breed: "Siamese",
      behavior: "Curious & Vocal",
      type: "cat"
    },
    {
      id: 7,
      name: "Charlie",
      image: "/img/adoption/adoption-7.png",
      gender: "Male",
      age: "3 years",
      breed: "Canary",
      behavior: "Cheerful Singer",
      type: "bird"
    },
    {
      id: 8,
      name: "Bella",
      image: "/img/adoption/adoption-8.png",
      gender: "Female",
      age: "2 years",
      breed: "Cockatiel",
      behavior: "Social & Smart",
      type: "bird"
    }
  ];

  const filteredPets = activeTab === 'all' ? pets : pets.filter(pet => pet.type === activeTab);

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>Adoption</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span> Adoption</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Adoption Top Area */}
      <section id="adoption_top_area" className="section_padding">
        <div className="container">
          {/* Section Heading */}
          <div className="row">
            <div className="col-lg-6 offset-lg-3">
              <div className="section_heading">
                <h2>Give a Loving Home</h2>
                <p>Change a life forever by opening your heart and home. Adopting a pet is a rewarding journey filled with love, laughter, and companionship.</p>
              </div>
            </div>
          </div>
          {/* Inner Content */}
          <div className="row">
            <div className="col-lg-6">
              <div className="service_details_content adoption_top_area_wrapper">
                <div className="service_details_left_top">
                  <h2>Find Your New Best Friend</h2>
                  <p>At PetsEnvy, we connect wonderful pets with caring families. Each animal in our care is waiting for a second chance at a happy life. Our adoption process is designed to ensure a perfect match for both you and your new companion.</p>
                </div>
                <p>
                  When you adopt, you're not just getting a pet; you're saving a life and making room for another animal in need.
                </p>
                <ul>
                  <li><img src="/img/icon/sm-leg.png" alt="img" />All pets are vet-checked and vaccinated.</li>
                  <li><img src="/img/icon/sm-leg.png" alt="img" />We provide behavior and history details.</li>
                  <li><img src="/img/icon/sm-leg.png" alt="img" />Post-adoption support is always available.</li>
                  <li><img src="/img/icon/sm-leg.png" alt="img" />Receive a starter kit with food and toys.</li>
                  <li><img src="/img/icon/sm-leg.png" alt="img" />Join a community of loving pet adopters.</li>
                </ul>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="adoption_big_img">
                <img src="/img/adoption/adoption_big.png" alt="img" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Adoption Main */}
      <section id="adoption_tab_area" className="section_padding_bottom">
        <div className="container">
          {/* Section Heading */}
          <div className="row">
            <div className="col-lg-6 offset-lg-3">
              <div className="section_heading">
                <h2>Adopt a Pet</h2>
                <p>Browse our available pets and meet your new family member. Each one has a unique story and is ready to be loved.</p>
              </div>
            </div>
          </div>
          <div className="row">
            <div className="col-lg-12">
              <div className="pet_price_wrapper">
                <div className="row">
                  <div className="col-lg-4 offset-lg-4">
                    <nav>
                      <div className="nav nav-tabs" id="nav-tab" role="tablist">
                        <button 
                          className={`nav-link ${activeTab === 'all' ? 'active' : ''}`}
                          onClick={() => setActiveTab('all')}
                          type="button"
                        >
                          All
                        </button>
                        <button 
                          className={`nav-link ${activeTab === 'dog' ? 'active' : ''}`}
                          onClick={() => setActiveTab('dog')}
                          type="button"
                        >
                          Dogs
                        </button>
                        <button 
                          className={`nav-link ${activeTab === 'cat' ? 'active' : ''}`}
                          onClick={() => setActiveTab('cat')}
                          type="button"
                        >
                          Cats
                        </button>
                        <button 
                          className={`nav-link ${activeTab === 'bird' ? 'active' : ''}`}
                          onClick={() => setActiveTab('bird')}
                          type="button"
                        >
                          Birds
                        </button>
                      </div>
                    </nav>
                  </div>
                </div>
                <div className="tab-content" id="nav-tabContent">
                  <div className="tab-pane fade show active">
                    <div className="adoption_tab_item_wrapper">
                      <div className="row">
                        {filteredPets.map((pet) => (
                          <div key={pet.id} className="col-lg-3 col-md-6 col-sm-12 col-12">
                            <div className="adoption_card_wrapper">
                              <div className="adoption_item_img img_hover">
                                <a href="#!">
                                  <img src={pet.image} alt="img" />
                                </a>
                              </div>
                              <div className="adoption_item_content">
                                <div className="adoption_info_btn">
                                  <a href="#!">More info</a>
                                </div>
                                <h3><a href="#!">{pet.name}</a></h3>
                                <ul>
                                  <li><span>Gender:</span> {pet.gender}</li>
                                  <li><span>Age:</span> {pet.age}</li>
                                  <li><span>Breed:</span> {pet.breed}</li>
                                  <li><span>Behave:</span> {pet.behavior}</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Adoption;
