import React from 'react';
import { Link } from 'react-router-dom';

const About = () => {
  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>About us</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span> About us</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Area */}
      <section id="about_area" className="section_padding">
        <div className="container">
          <div className="row">
            <div className="col-lg-6">
              <div className="about_area_left_wrapper">
                <div className="about_left_content_top">
                  <h2>Best Agency For Your Pet</h2>
                  <p>
                    We provide expert care, warm attention, and smart solutions for your pets. From daily needs to specialized services, our team ensures comfort, safety, and happiness at every step—because your pet deserves nothing less than the best.
                  </p>
                  <div className="about_round_check_wrapper">
                    <div className="about_round_check_item">
                      <div className="about_round_check_inner">
                        <img src="/img/icon/round-check.png" alt="icon" />
                        <p>Certified goomer</p>
                      </div>
                      <div className="about_round_check_inner">
                        <img src="/img/icon/round-check.png" alt="icon" />
                        <p>Pet care service</p>
                      </div>
                    </div>
                    <div className="about_round_check_item">
                      <div className="about_round_check_inner">
                        <img src="/img/icon/round-check.png" alt="icon" />
                        <p>Animal lover</p>
                      </div>
                      <div className="about_round_check_inner">
                        <img src="/img/icon/round-check.png" alt="icon" />
                        <p>Adoption pets</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="about_left_content_bottom">
                  <div className="accordion" id="accordionExample">
                    <div className="accordion-item">
                      <h2 className="accordion-header" id="headingOne">
                        <button className="accordion-button" type="button" data-bs-toggle="collapse"
                          data-bs-target="#collapseOne" aria-expanded="true"
                          aria-controls="collapseOne">
                          1. What services does your pet care agency offer?
                        </button>
                      </h2>
                      <div id="collapseOne" className="accordion-collapse collapse show"
                        aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                        <div className="accordion-body">
                          <p>
                            We offer a wide range of services including grooming, veterinary checkups, pet sitting, training, vaccinations, and emergency care. Whether it's a quick nail trim or long-term boarding, we're here to keep your pet healthy and happy.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="accordion-item">
                      <h2 className="accordion-header" id="headingTwo">
                        <button className="accordion-button collapsed" type="button"
                          data-bs-toggle="collapse" data-bs-target="#collapseTwo"
                          aria-expanded="false" aria-controls="collapseTwo">
                          2. Are your staff trained and certified in pet care?
                        </button>
                      </h2>
                      <div id="collapseTwo" className="accordion-collapse collapse"
                        aria-labelledby="headingTwo" data-bs-parent="#accordionExample">
                        <div className="accordion-body">
                          <p>
                            Yes! Our team consists of certified professionals and trained animal lovers with years of hands-on experience. We prioritize your pet's safety, comfort, and emotional well-being in every service we provide.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="accordion-item">
                      <h2 className="accordion-header" id="headingThree">
                        <button className="accordion-button collapsed" type="button"
                          data-bs-toggle="collapse" data-bs-target="#collapseThree"
                          aria-expanded="false" aria-controls="collapseThree">
                          3. How do I book an appointment or consultation?
                        </button>
                      </h2>
                      <div id="collapseThree" className="accordion-collapse collapse"
                        aria-labelledby="headingThree" data-bs-parent="#accordionExample">
                        <div className="accordion-body">
                          <p>
                            Booking is simple! You can schedule an appointment directly through our website, call our customer service line, or visit us in person. We also offer online consultations for added convenience.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="about_area_right_img">
                <img src="/img/common/about.png" alt="img" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI Consultation Area */}
      <section id="our_consultation_area" className="section_padding bg_section">
        <div className="container">
          {/* Section Heading */}
          <div className="row">
            <div className="col-lg-6 offset-lg-3">
              <div className="section_heading">
                <h2>AI-Powered Pet Skin Consultation</h2>
                <p>Upload a photo of your dog's skin condition and let our intelligent diagnostic system analyze and provide instant insights powered by machine learning.</p>
              </div>
            </div>
          </div>

          {/* AI Steps */}
          <div className="row">
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="our_consultation_item">
                <img src="/img/icon/consultation-1.png" alt="img" />
                <h3>1. Upload Image</h3>
                <p>Choose a clear photo showing your dog's skin issue. Our AI performs best with close, well-lit images.</p>
                <img src="/img/element/arrow-down.png" alt="img" className="arrow_down_consult" />
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="our_consultation_item">
                <img src="/img/icon/consultation-2.png" alt="img" />
                <h3>2. AI Analysis</h3>
                <p>Our AI model detects signs of common skin diseases such as infections, rashes, or dermatitis within seconds.</p>
                <img src="/img/element/arrow-up.png" alt="img" className="arrow_up_consult" />
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="our_consultation_item">
                <img src="/img/icon/consultation-3.png" alt="img" />
                <h3>3. Instant Report</h3>
                <p>Receive a preliminary diagnosis, risk level, and recommendations right away—no waiting room needed!</p>
                <img src="/img/element/arrow-down.png" alt="img" className="arrow_down_consult" />
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="our_consultation_item">
                <img src="/img/icon/consultation-4.png" alt="img" />
                <h3>4. Expert Follow-Up</h3>
                <p>If needed, connect with our vet partners for a full consultation or treatment options based on AI results.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Team Area */}
      <section id="our_groomers_area" className="section_padding">
        <div className="container">
          {/* Section Heading */}
          <div className="row">
            <div className="col-lg-6 offset-lg-3">
              <div className="section_heading">
                <h2>Meet our groomers</h2>
                <p>Meet our expert groomers who bring gentle care, skill, and love to every pet they pamper because your furry friend deserves the best.</p>
              </div>
            </div>
          </div>
          {/* Inner Content */}
          <div className="row">
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="groomers_area_wrapper">
                <div className="groomers_area_img img_animation_one">
                  <img src="/img/groomers/Dr1.jpg" alt="img" />
                </div>
                <div className="groomers_area_decantation">
                  <div className="groomers_social_icon">
                    <ul>
                      <li><a href="#!"><i className="fab fa-facebook-f"></i></a></li>
                      <li><a href="#!"><i className="fab fa-twitter"></i></a></li>
                      <li><a href="#!"><i className="fab fa-instagram"></i></a></li>
                      <li><a href="#!"><i className="fab fa-linkedin-in"></i></a></li>
                    </ul>
                  </div>
                  <h3>Adeeb Rizvi</h3>
                  <p>Pet trainer</p>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="groomers_area_wrapper">
                <div className="groomers_area_img img_animation_one">
                  <img src="/img/groomers/Dr3.jpg" alt="img" />
                </div>
                <div className="groomers_area_decantation">
                  <div className="groomers_social_icon">
                    <ul>
                      <li><a href="#!"><i className="fab fa-facebook-f"></i></a></li>
                      <li><a href="#!"><i className="fab fa-twitter"></i></a></li>
                      <li><a href="#!"><i className="fab fa-instagram"></i></a></li>
                      <li><a href="#!"><i className="fab fa-linkedin-in"></i></a></li>
                    </ul>
                  </div>
                  <h3>Abdul Bari Khan</h3>
                  <p>Veterinarian</p>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="groomers_area_wrapper">
                <div className="groomers_area_img img_animation_one">
                  <img src="/img/groomers/fake.jpg" alt="img" />
                </div>
                <div className="groomers_area_decantation">
                  <div className="groomers_social_icon">
                    <ul>
                      <li><a href="#!"><i className="fab fa-facebook-f"></i></a></li>
                      <li><a href="#!"><i className="fab fa-twitter"></i></a></li>
                      <li><a href="#!"><i className="fab fa-instagram"></i></a></li>
                      <li><a href="#!"><i className="fab fa-linkedin-in"></i></a></li>
                    </ul>
                  </div>
                  <h3>Fredrich monn</h3>
                  <p>Pet trainer</p>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-12 col-12">
              <div className="groomers_area_wrapper">
                <div className="groomers_area_img img_animation_one">
                  <img src="/img/groomers/Dr5.jpg" alt="img" />
                </div>
                <div className="groomers_area_decantation">
                  <div className="groomers_social_icon">
                    <ul>
                      <li><a href="#!"><i className="fab fa-facebook-f"></i></a></li>
                      <li><a href="#!"><i className="fab fa-twitter"></i></a></li>
                      <li><a href="#!"><i className="fab fa-instagram"></i></a></li>
                      <li><a href="#!"><i className="fab fa-linkedin-in"></i></a></li>
                    </ul>
                  </div>
                  <h3>Tahir Shamsi</h3>
                  <p>Pet trainer</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Counter Area */}
      <section id="our_counter_area" className="section_padding_bottom">
        <div className="container">
          <div className="counter_area_wrapper">
            <div className="row">
              <div className="col-lg-3 col-md-6 col-sm-12 col-12">
                <div className="our_counter_item">
                  <img src="/img/icon/counter-1.png" alt="img" />
                  <h2 className="counter">348</h2>
                  <p>Pet groomers</p>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-12 col-12">
                <div className="our_counter_item">
                  <img src="/img/icon/counter-2.png" alt="img" />
                  <h2 className="counter">2748</h2>
                  <p>Adopted pet</p>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-12 col-12">
                <div className="our_counter_item">
                  <img src="/img/icon/counter-3.png" alt="img" />
                  <h2 className="counter">4287</h2>
                  <p>Awards won</p>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-12 col-12">
                <div className="our_counter_item">
                  <img src="/img/icon/counter-4.png" alt="img" />
                  <h2 className="counter">8294</h2>
                  <p>Clients</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Video Area */}
      <section id="video_area_main">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 offset-lg-2">
              <div className="video_area_content">
                <h2>We Care For Your Pet</h2>
                <p>Discover how we provide compassionate, expert care in a safe and loving environment. Watch our team in action and see why pet parents trust us.</p>
                <a href="https://vimeo.com/45830194" className="vedio_btn popup-vimeo">
                  <img src="/img/icon/btn-video.png" alt="icon" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default About;
