import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useModal } from '../contexts/ModalContext';
import { showSuccessToast, showErrorToast, showWarningToast } from '../utils/toast';
import '../components/FormValidation.css';

const MyAccount = () => {
  const { currentUser, login, signup, logout, resetPassword } = useAuth();
  const navigate = useNavigate();
  const { showSuccessModal, showErrorModal, showWarningModal } = useModal();
  const [loginLoading, setLoginLoading] = useState(false);
  const [registerLoading, setRegisterLoading] = useState(false);
  const [resetLoading, setResetLoading] = useState(false);

  const [loginData, setLoginData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });

  const [registerData, setRegisterData] = useState({
    username: '',
    email: '',
    password: ''
  });

  const handleLoginChange = (e) => {
    const { name, value, type, checked } = e.target;
    setLoginData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleRegisterChange = (e) => {
    const { name, value } = e.target;
    setRegisterData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLoginSubmit = async (e) => {
    e.preventDefault();

    // Trim whitespace from inputs
    const trimmedEmail = loginData.email.trim();
    const trimmedPassword = loginData.password.trim();

    if (!trimmedEmail || !trimmedPassword) {
      showWarningModal('Missing Information', 'Please fill in all required fields.');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(trimmedEmail)) {
      showWarningModal('Invalid Email', 'Please enter a valid email address.');
      return;
    }

    try {
      setLoginLoading(true);
      await login(trimmedEmail, trimmedPassword);
      navigate('/dashboard');
    } catch (error) {
      // Error is already handled in AuthContext with toast
      console.error('Login error:', error);
    } finally {
      setLoginLoading(false);
    }
  };

  const handleRegisterSubmit = async (e) => {
    e.preventDefault();

    // Trim whitespace from inputs
    const trimmedData = {
      email: registerData.email.trim(),
      password: registerData.password.trim(),
      username: registerData.username.trim()
    };

    if (!trimmedData.email || !trimmedData.password || !trimmedData.username) {
      showWarningModal('Missing Information', 'Please fill in all required fields.');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(trimmedData.email)) {
      showWarningModal('Invalid Email', 'Please enter a valid email address.');
      return;
    }

    if (trimmedData.password.length < 6) {
      showWarningModal('Weak Password', 'Password must be at least 6 characters long.');
      return;
    }

    if (trimmedData.username.length < 2) {
      showWarningModal('Invalid Name', 'Name must be at least 2 characters long.');
      return;
    }

    try {
      setRegisterLoading(true);
      await signup(trimmedData.email, trimmedData.password, trimmedData.username);
      navigate('/dashboard');
    } catch (error) {
      // Error is already handled in AuthContext with toast
      console.error('Registration error:', error);
    } finally {
      setRegisterLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!loginData.email) {
      showWarningModal('Email Required', 'Please enter your email address first.');
      return;
    }

    try {
      setResetLoading(true);
      await resetPassword(loginData.email);
      // Success is already handled in AuthContext with toast
    } catch (error) {
      // Error is already handled in AuthContext with toast
      console.error('Password reset error:', error);
    } finally {
      setResetLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      showErrorModal('Logout Failed', 'Failed to log out. Please try again.');
    }
  };

  // If user is already logged in, show dashboard link
  if (currentUser) {
    return (
      <>
        {/* Common Banner */}
        <section id="common_area_banner">
          <div className="container">
            <div className="row">
              <div className="col-lg-12">
                <div className="common_banner_content">
                  <h2>My Account</h2>
                  <ul>
                    <li><Link to="/">Home</Link></li>
                    <li><span>/</span>My Account</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Logged In User Area */}
        <section id="my_account_main_area" className="section_padding">
          <div className="container">
            <div className="row">
              <div className="col-lg-8 offset-lg-2">
                <div className="text-center">
                  <h2>Welcome back, {currentUser.displayName || currentUser.email}!</h2>
                  <p className="mb-4">You are successfully logged in.</p>
                  <div className="d-flex justify-content-center gap-3">
                    <Link to="/dashboard" className="btn btn_theme btn_md">Go to Dashboard</Link>
                    <button onClick={handleLogout} className="btn btn-outline-secondary btn_md">Logout</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </>
    );
  }

  return (
    <>
      {/* Common Banner */}
      <section id="common_area_banner">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="common_banner_content">
                <h2>My Account</h2>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><span>/</span>My Account</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* My account Area */}
      <section id="my_account_main_area" className="section_padding">
        <div className="container">
          {/* Section Heading */}
          <div className="row">
            <div className="col-lg-6 offset-lg-3">
              <div className="section_heading">
                <h2>Login or Register</h2>
                <p>Access your account to manage appointments, view your pet's history, and enjoy a seamless experience with PetsEnvy.</p>
              </div>
            </div>
          </div>
          {/* Inner Content */}
          <div className="row">
            <div className="col-lg-6">
              <div className="my_account_wrapper my_account_wrapper_left">
                <div className="service_details_left_top">
                  <h2>Login to Your Account</h2>
                  <div className="my_account_inner">
                    <form onSubmit={handleLoginSubmit}>
                      <div className="form-group">
                        <input 
                          type="email" 
                          className="form-control" 
                          placeholder="Email address" 
                          name="email"
                          value={loginData.email}
                          onChange={handleLoginChange}
                          required 
                        />
                      </div>
                      <div className="form-group">
                        <input 
                          type="password" 
                          className="form-control" 
                          placeholder="Password" 
                          name="password"
                          value={loginData.password}
                          onChange={handleLoginChange}
                          required 
                        />
                      </div>
                      <div className="my_account_bottom_wrapper">
                        <div className="form-check">
                          <input 
                            className="form-check-input" 
                            type="checkbox" 
                            name="rememberMe"
                            id="flexCheckDefault"
                            checked={loginData.rememberMe}
                            onChange={handleLoginChange}
                          />
                          <label className="form-check-label" htmlFor="flexCheckDefault">
                            Remember me
                          </label>
                        </div>
                        <div className="forget_pass">
                          <button
                            type="button"
                            onClick={handleForgotPassword}
                            className="btn btn-link p-0"
                            disabled={resetLoading}
                          >
                            {resetLoading ? 'Sending...' : 'Forgot password?'}
                          </button>
                        </div>
                      </div>
                      <div className="my_acount_submit">
                        <button type="submit" className="btn btn_theme btn_md w-100" disabled={loginLoading}>
                          {loginLoading ? 'Logging in...' : 'Log In'}
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="my_account_wrapper my_account_wrapper_right">
                <div className="service_details_left_top">
                  <h2>Create a New Account</h2>
                  <div className="my_account_inner">
                    <form onSubmit={handleRegisterSubmit}>
                      <div className="form-group">
                        <input 
                          type="text" 
                          className="form-control" 
                          placeholder="Username" 
                          name="username"
                          value={registerData.username}
                          onChange={handleRegisterChange}
                          required 
                        />
                      </div>
                      <div className="form-group">
                        <input 
                          type="email" 
                          className="form-control" 
                          placeholder="Email address" 
                          name="email"
                          value={registerData.email}
                          onChange={handleRegisterChange}
                          required 
                        />
                      </div>
                      <div className="form-group">
                        <input 
                          type="password" 
                          className="form-control" 
                          placeholder="Password" 
                          name="password"
                          value={registerData.password}
                          onChange={handleRegisterChange}
                          required 
                        />
                      </div>
                      <div className="my_account_bottom_wrapper">
                        <p>Your personal data will be used to support your experience throughout this website, to manage access to your account, and for other purposes described in our privacy policy.</p>
                      </div>
                      <div className="my_acount_submit">
                        <button type="submit" className="btn btn_theme btn_md w-100" disabled={registerLoading}>
                          {registerLoading ? 'Creating Account...' : 'Register Now'}
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe Area */}
      <section id="subscribe_area">
        <div className="container">
          <div className="subscribe_wrapper">
            <div className="row align-items-center">
              <div className="col-lg-4">
                <div className="subscribe_text">
                  <p>Newsletter</p>
                  <h3 className="heading_main_subscribe">To get weekly & monthly news,
                    <span>Subscribe</span> to our newsletter.
                  </h3>
                </div>
              </div>
              <div className="col-lg-6 offset-lg-2">
                <div className="cta_right_side">
                  <form action="#!" id="subscribe_form">
                    <div className="input-group">
                      <input type="text" className="form-control" placeholder="Your mail address" required />
                      <button className="btn btn_theme btn_md" type="submit">Subscribe</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default MyAccount;
