import React, { createContext, useContext, useState, useEffect } from 'react';
import { doc, setDoc, getDoc, onSnapshot } from 'firebase/firestore';
import { db } from '../firebase/config';
import { useAuth } from './AuthContext';
import { showSuccessToast, showErrorToast } from '../utils/toast';

const CartContext = createContext();

export function useCart() {
  return useContext(CartContext);
}

export function CartProvider({ children }) {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const { currentUser } = useAuth();

  // Load cart from Firebase when user logs in
  useEffect(() => {
    if (currentUser) {
      const cartRef = doc(db, 'carts', currentUser.uid);
      const unsubscribe = onSnapshot(cartRef, (doc) => {
        if (doc.exists()) {
          setCartItems(doc.data().items || []);
        } else {
          setCartItems([]);
        }
      });

      return () => unsubscribe();
    } else {
      // Load cart from localStorage for guest users
      const savedCart = localStorage.getItem('petsenvy_cart');
      if (savedCart) {
        setCartItems(JSON.parse(savedCart));
      }
    }
  }, [currentUser]);

  // Save cart to Firebase or localStorage
  const saveCart = async (items) => {
    if (currentUser) {
      try {
        await setDoc(doc(db, 'carts', currentUser.uid), {
          items: items,
          updatedAt: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error saving cart to Firebase:', error);
      }
    } else {
      localStorage.setItem('petsenvy_cart', JSON.stringify(items));
    }
  };

  // Add item to cart
  const addToCart = async (product, quantity = 1) => {
    setLoading(true);
    try {
      const existingItemIndex = cartItems.findIndex(item => item.id === product.id);
      let newItems;

      if (existingItemIndex > -1) {
        // Update quantity if item already exists
        newItems = cartItems.map((item, index) => 
          index === existingItemIndex 
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      } else {
        // Add new item
        newItems = [...cartItems, { ...product, quantity }];
      }

      setCartItems(newItems);
      await saveCart(newItems);
    } catch (error) {
      console.error('Error adding to cart:', error);
      showErrorToast('Failed to add item to cart');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Remove item from cart
  const removeFromCart = async (productId) => {
    setLoading(true);
    try {
      const newItems = cartItems.filter(item => item.id !== productId);
      setCartItems(newItems);
      await saveCart(newItems);
    } catch (error) {
      console.error('Error removing from cart:', error);
      showErrorToast('Failed to remove item from cart');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update item quantity
  const updateQuantity = async (productId, quantity) => {
    if (quantity <= 0) {
      await removeFromCart(productId);
      return;
    }

    setLoading(true);
    try {
      const newItems = cartItems.map(item =>
        item.id === productId ? { ...item, quantity } : item
      );
      setCartItems(newItems);
      await saveCart(newItems);
    } catch (error) {
      console.error('Error updating quantity:', error);
      showErrorToast('Failed to update item quantity');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Clear cart
  const clearCart = async () => {
    setLoading(true);
    try {
      setCartItems([]);
      await saveCart([]);
    } catch (error) {
      console.error('Error clearing cart:', error);
      showErrorToast('Failed to clear cart');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Get cart total
  const getCartTotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  // Get cart item count
  const getCartItemCount = () => {
    return cartItems.reduce((count, item) => count + item.quantity, 0);
  };

  const value = {
    cartItems,
    loading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getCartTotal,
    getCartItemCount
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
}
