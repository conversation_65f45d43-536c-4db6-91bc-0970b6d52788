import React, { createContext, useContext, useState, useEffect } from 'react';
import { collection, addDoc, getDocs, query, where, updateDoc, doc, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';
import { useAuth } from './AuthContext';
import { showSuccessToast, showErrorToast } from '../utils/toast';

const BookingContext = createContext();

export const useBooking = () => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
};

export const BookingProvider = ({ children }) => {
  const { currentUser } = useAuth();
  const [bookings, setBookings] = useState([]);
  const [subscriptions, setSubscriptions] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load user's bookings, subscriptions, and orders
  useEffect(() => {
    if (currentUser) {
      loadUserData();
    } else {
      setBookings([]);
      setSubscriptions([]);
      setOrders([]);
    }
  }, [currentUser]);

  const loadUserData = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);

      // Load service bookings
      const bookingsQuery = query(
        collection(db, 'serviceBookings'),
        where('userId', '==', currentUser.uid),
        orderBy('createdAt', 'desc')
      );
      const bookingsSnapshot = await getDocs(bookingsQuery);
      const userBookings = bookingsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setBookings(userBookings);

      // Load subscriptions
      const subscriptionsQuery = query(
        collection(db, 'subscriptions'),
        where('userId', '==', currentUser.uid),
        orderBy('createdAt', 'desc')
      );
      const subscriptionsSnapshot = await getDocs(subscriptionsQuery);
      const userSubscriptions = subscriptionsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setSubscriptions(userSubscriptions);

      // Load orders
      const ordersQuery = query(
        collection(db, 'orders'),
        where('userId', '==', currentUser.uid),
        orderBy('createdAt', 'desc')
      );
      const ordersSnapshot = await getDocs(ordersQuery);
      const userOrders = ordersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setOrders(userOrders);

    } catch (error) {
      console.error('Error loading user data:', error);
      showErrorToast('Failed to load your data');
    } finally {
      setLoading(false);
    }
  };

  // Book a service
  const bookService = async (service, bookingDetails = {}) => {
    if (!currentUser) {
      throw new Error('User must be logged in to book a service');
    }

    try {
      setLoading(true);

      const bookingData = {
        userId: currentUser.uid,
        userEmail: currentUser.email,
        userName: currentUser.displayName || 'User',
        serviceId: service.id,
        serviceName: service.name,
        servicePrice: service.price,
        serviceDescription: service.description,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...bookingDetails
      };

      const docRef = await addDoc(collection(db, 'serviceBookings'), bookingData);
      
      const newBooking = {
        id: docRef.id,
        ...bookingData
      };

      setBookings(prev => [newBooking, ...prev]);
      showSuccessToast(`${service.name} booked successfully!`);
      
      return newBooking;
    } catch (error) {
      console.error('Error booking service:', error);
      showErrorToast('Failed to book service');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Subscribe to a plan
  const subscribeToPlan = async (plan, subscriptionDetails = {}) => {
    if (!currentUser) {
      throw new Error('User must be logged in to subscribe');
    }

    try {
      setLoading(true);

      const subscriptionData = {
        userId: currentUser.uid,
        userEmail: currentUser.email,
        userName: currentUser.displayName || 'User',
        planId: plan.id,
        planName: plan.name,
        planPrice: plan.price,
        planDuration: plan.duration || 'monthly',
        planFeatures: plan.features || [],
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        nextBillingDate: getNextBillingDate(plan.duration || 'monthly'),
        ...subscriptionDetails
      };

      const docRef = await addDoc(collection(db, 'subscriptions'), subscriptionData);
      
      const newSubscription = {
        id: docRef.id,
        ...subscriptionData
      };

      setSubscriptions(prev => [newSubscription, ...prev]);
      showSuccessToast(`Subscribed to ${plan.name} successfully!`);
      
      return newSubscription;
    } catch (error) {
      console.error('Error subscribing to plan:', error);
      showErrorToast('Failed to subscribe to plan');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Cancel a booking
  const cancelBooking = async (bookingId) => {
    try {
      setLoading(true);

      await updateDoc(doc(db, 'serviceBookings', bookingId), {
        status: 'cancelled',
        updatedAt: new Date().toISOString()
      });

      setBookings(prev => 
        prev.map(booking => 
          booking.id === bookingId 
            ? { ...booking, status: 'cancelled', updatedAt: new Date().toISOString() }
            : booking
        )
      );

      showSuccessToast('Booking cancelled successfully');
    } catch (error) {
      console.error('Error cancelling booking:', error);
      showErrorToast('Failed to cancel booking');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Cancel a subscription
  const cancelSubscription = async (subscriptionId) => {
    try {
      setLoading(true);

      await updateDoc(doc(db, 'subscriptions', subscriptionId), {
        status: 'cancelled',
        updatedAt: new Date().toISOString(),
        cancelledAt: new Date().toISOString()
      });

      setSubscriptions(prev => 
        prev.map(subscription => 
          subscription.id === subscriptionId 
            ? { 
                ...subscription, 
                status: 'cancelled', 
                updatedAt: new Date().toISOString(),
                cancelledAt: new Date().toISOString()
              }
            : subscription
        )
      );

      showSuccessToast('Subscription cancelled successfully');
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      showErrorToast('Failed to cancel subscription');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Helper function to calculate next billing date
  const getNextBillingDate = (duration) => {
    const now = new Date();
    switch (duration) {
      case 'weekly':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString();
      case 'monthly':
        return new Date(now.getFullYear(), now.getMonth() + 1, now.getDate()).toISOString();
      case 'yearly':
        return new Date(now.getFullYear() + 1, now.getMonth(), now.getDate()).toISOString();
      default:
        return new Date(now.getFullYear(), now.getMonth() + 1, now.getDate()).toISOString();
    }
  };

  // Create order (for checkout)
  const createOrder = async (orderData) => {
    if (!currentUser) {
      throw new Error('User must be logged in to place an order');
    }

    try {
      setLoading(true);

      const order = {
        userId: currentUser.uid,
        userEmail: currentUser.email,
        userName: currentUser.displayName || 'User',
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...orderData
      };

      const docRef = await addDoc(collection(db, 'orders'), order);
      
      const newOrder = {
        id: docRef.id,
        ...order
      };

      setOrders(prev => [newOrder, ...prev]);
      showSuccessToast('Order placed successfully!');
      
      return newOrder;
    } catch (error) {
      console.error('Error creating order:', error);
      showErrorToast('Failed to place order');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    bookings,
    subscriptions,
    orders,
    loading,
    bookService,
    subscribeToPlan,
    cancelBooking,
    cancelSubscription,
    createOrder,
    loadUserData
  };

  return (
    <BookingContext.Provider value={value}>
      {children}
    </BookingContext.Provider>
  );
};
