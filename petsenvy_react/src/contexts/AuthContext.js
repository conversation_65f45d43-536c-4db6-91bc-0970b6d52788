import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  onAuthStateChanged
} from 'firebase/auth';
import { doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from '../firebase/config';
import { showSuccessToast, showErrorToast } from '../utils/toast';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sign up function
  async function signup(email, password, displayName) {
    try {
      // Validate inputs
      if (!email || !password || !displayName) {
        throw new Error('All fields are required');
      }

      if (password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      const result = await createUserWithEmailAndPassword(auth, email, password);

      // Update the user's display name
      await updateProfile(result.user, {
        displayName: displayName
      });

      // Create user profile in Firestore
      const userProfile = {
        uid: result.user.uid,
        email: email,
        displayName: displayName,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        subscriptions: [],
        orders: [],
        services: []
      };

      await setDoc(doc(db, 'users', result.user.uid), userProfile);

      showSuccessToast(`Welcome ${displayName}! Your account has been created successfully.`);
      return result;
    } catch (error) {
      console.error('Signup error:', error);
      let errorMessage = 'Failed to create account. Please try again.';

      switch (error.code) {
        case 'auth/email-already-in-use':
          errorMessage = 'An account with this email already exists.';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'auth/weak-password':
          errorMessage = 'Password is too weak. Please choose a stronger password.';
          break;
        case 'auth/operation-not-allowed':
          errorMessage = 'Firebase Authentication is not enabled. Please check the setup instructions.';
          // Show detailed setup instructions
          setTimeout(() => {
            showErrorToast('Please enable Email/Password authentication in Firebase Console. Check the FIREBASE_SETUP.md file for detailed instructions.');
          }, 1000);
          break;
        case 'auth/network-request-failed':
          errorMessage = 'Network error. Please check your connection and try again.';
          break;
        default:
          errorMessage = error.message || errorMessage;
      }

      showErrorToast(errorMessage);
      throw new Error(errorMessage);
    }
  }

  // Login function
  async function login(email, password) {
    try {
      // Validate inputs
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      const result = await signInWithEmailAndPassword(auth, email, password);

      // Get user profile from Firestore
      const userDoc = await getDoc(doc(db, 'users', result.user.uid));
      if (userDoc.exists()) {
        setUserProfile(userDoc.data());
      }

      showSuccessToast(`Welcome back, ${result.user.displayName || 'User'}!`);
      return result;
    } catch (error) {
      console.error('Login error:', error);
      let errorMessage = 'Failed to log in. Please try again.';

      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email address.';
          break;
        case 'auth/wrong-password':
          errorMessage = 'Incorrect password. Please try again.';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'auth/user-disabled':
          errorMessage = 'This account has been disabled. Please contact support.';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later.';
          break;
        case 'auth/operation-not-allowed':
          errorMessage = 'Firebase Authentication is not enabled. Please check the setup instructions.';
          // Show detailed setup instructions
          setTimeout(() => {
            showErrorToast('Please enable Email/Password authentication in Firebase Console. Check the FIREBASE_SETUP.md file for detailed instructions.');
          }, 1000);
          break;
        case 'auth/network-request-failed':
          errorMessage = 'Network error. Please check your connection and try again.';
          break;
        default:
          errorMessage = error.message || errorMessage;
      }

      showErrorToast(errorMessage);
      throw new Error(errorMessage);
    }
  }

  // Logout function
  async function logout() {
    try {
      await signOut(auth);
      setUserProfile(null);
      showSuccessToast('You have been logged out successfully.');
    } catch (error) {
      console.error('Logout error:', error);
      showErrorToast('Failed to log out. Please try again.');
      throw error;
    }
  }

  // Reset password function
  async function resetPassword(email) {
    try {
      if (!email) {
        throw new Error('Email address is required');
      }

      await sendPasswordResetEmail(auth, email);
      showSuccessToast('Password reset email sent! Check your inbox.');
    } catch (error) {
      console.error('Password reset error:', error);
      let errorMessage = 'Failed to send password reset email.';

      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email address.';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'auth/network-request-failed':
          errorMessage = 'Network error. Please check your connection and try again.';
          break;
        default:
          errorMessage = error.message || errorMessage;
      }

      showErrorToast(errorMessage);
      throw new Error(errorMessage);
    }
  }

  // Update user profile
  async function updateUserProfile(updates) {
    if (currentUser) {
      const userRef = doc(db, 'users', currentUser.uid);
      await setDoc(userRef, updates, { merge: true });
      setUserProfile(prev => ({ ...prev, ...updates }));
    }
  }

  // Get user profile from Firestore
  async function getUserProfile(uid) {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid));
      if (userDoc.exists()) {
        return userDoc.data();
      }
      return null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setCurrentUser(user);
        // Get user profile from Firestore
        const profile = await getUserProfile(user.uid);
        setUserProfile(profile);
      } else {
        setCurrentUser(null);
        setUserProfile(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    userProfile,
    signup,
    login,
    logout,
    resetPassword,
    updateUserProfile,
    getUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}
