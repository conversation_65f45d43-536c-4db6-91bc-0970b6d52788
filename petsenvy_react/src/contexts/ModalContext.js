import React, { createContext, useContext, useState } from 'react';
import CustomModal from '../components/CustomModal';

const ModalContext = createContext();

export function useModal() {
  return useContext(ModalContext);
}

export function ModalProvider({ children }) {
  const [modal, setModal] = useState({
    isOpen: false,
    title: '',
    message: '',
    type: 'info',
    confirmText: 'OK',
    cancelText: 'Cancel',
    showCancel: false,
    onConfirm: null,
    onCancel: null,
  });

  const showModal = ({
    title,
    message,
    type = 'info',
    confirmText = 'OK',
    cancelText = 'Cancel',
    showCancel = false,
    onConfirm = null,
    onCancel = null,
  }) => {
    setModal({
      isOpen: true,
      title,
      message,
      type,
      confirmText,
      cancelText,
      showCancel,
      onConfirm,
      onCancel,
    });
  };

  const hideModal = () => {
    setModal(prev => ({ ...prev, isOpen: false }));
  };

  // Convenience methods
  const showSuccessModal = (title, message, onConfirm = null) => {
    showModal({
      title,
      message,
      type: 'success',
      confirmText: 'Great!',
      onConfirm,
    });
  };

  const showErrorModal = (title, message, onConfirm = null) => {
    showModal({
      title,
      message,
      type: 'error',
      confirmText: 'OK',
      onConfirm,
    });
  };

  const showWarningModal = (title, message, onConfirm = null) => {
    showModal({
      title,
      message,
      type: 'warning',
      confirmText: 'Understood',
      onConfirm,
    });
  };

  const showConfirmModal = (title, message, onConfirm, onCancel = null) => {
    showModal({
      title,
      message,
      type: 'confirm',
      confirmText: 'Yes',
      cancelText: 'No',
      showCancel: true,
      onConfirm,
      onCancel,
    });
  };

  const showInfoModal = (title, message, onConfirm = null) => {
    showModal({
      title,
      message,
      type: 'info',
      confirmText: 'OK',
      onConfirm,
    });
  };

  const value = {
    showModal,
    hideModal,
    showSuccessModal,
    showErrorModal,
    showWarningModal,
    showConfirmModal,
    showInfoModal,
  };

  return (
    <ModalContext.Provider value={value}>
      {children}
      <CustomModal
        isOpen={modal.isOpen}
        onClose={hideModal}
        title={modal.title}
        message={modal.message}
        type={modal.type}
        confirmText={modal.confirmText}
        cancelText={modal.cancelText}
        showCancel={modal.showCancel}
        onConfirm={modal.onConfirm}
        onCancel={modal.onCancel}
      />
    </ModalContext.Provider>
  );
}
