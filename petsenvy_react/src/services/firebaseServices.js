import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../firebase/config';

// Products Services
export const productServices = {
  // Get all products
  async getAllProducts() {
    try {
      const querySnapshot = await getDocs(collection(db, 'products'));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting products:', error);
      throw error;
    }
  },

  // Get product by ID
  async getProductById(id) {
    try {
      const docRef = doc(db, 'products', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      } else {
        throw new Error('Product not found');
      }
    } catch (error) {
      console.error('Error getting product:', error);
      throw error;
    }
  },

  // Add new product (admin only)
  async addProduct(productData) {
    try {
      const docRef = await addDoc(collection(db, 'products'), {
        ...productData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  },

  // Update product (admin only)
  async updateProduct(id, updates) {
    try {
      const docRef = doc(db, 'products', id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  },

  // Delete product (admin only)
  async deleteProduct(id) {
    try {
      await deleteDoc(doc(db, 'products', id));
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }
};

// Services Services
export const servicesServices = {
  // Get all services
  async getAllServices() {
    try {
      const querySnapshot = await getDocs(collection(db, 'services'));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting services:', error);
      throw error;
    }
  },

  // Get service by ID
  async getServiceById(id) {
    try {
      const docRef = doc(db, 'services', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      } else {
        throw new Error('Service not found');
      }
    } catch (error) {
      console.error('Error getting service:', error);
      throw error;
    }
  },

  // Book a service
  async bookService(userId, serviceId, bookingData) {
    try {
      const docRef = await addDoc(collection(db, 'serviceBookings'), {
        userId,
        serviceId,
        ...bookingData,
        status: 'pending',
        createdAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error booking service:', error);
      throw error;
    }
  },

  // Get user's service bookings
  async getUserServiceBookings(userId) {
    try {
      const q = query(
        collection(db, 'serviceBookings'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting user service bookings:', error);
      throw error;
    }
  }
};

// Subscription Services
export const subscriptionServices = {
  // Get all subscription plans
  async getAllPlans() {
    try {
      const querySnapshot = await getDocs(collection(db, 'subscriptionPlans'));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting subscription plans:', error);
      throw error;
    }
  },

  // Subscribe to a plan
  async subscribeToPlan(userId, planId, subscriptionData) {
    try {
      const docRef = await addDoc(collection(db, 'subscriptions'), {
        userId,
        planId,
        ...subscriptionData,
        status: 'active',
        createdAt: serverTimestamp(),
        nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      });
      return docRef.id;
    } catch (error) {
      console.error('Error subscribing to plan:', error);
      throw error;
    }
  },

  // Get user's subscriptions
  async getUserSubscriptions(userId) {
    try {
      const q = query(
        collection(db, 'subscriptions'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting user subscriptions:', error);
      throw error;
    }
  },

  // Cancel subscription
  async cancelSubscription(subscriptionId) {
    try {
      const docRef = doc(db, 'subscriptions', subscriptionId);
      await updateDoc(docRef, {
        status: 'cancelled',
        cancelledAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw error;
    }
  }
};

// Order Services
export const orderServices = {
  // Create new order
  async createOrder(userId, orderData) {
    try {
      const docRef = await addDoc(collection(db, 'orders'), {
        userId,
        ...orderData,
        status: 'pending',
        createdAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  },

  // Get user's orders
  async getUserOrders(userId) {
    try {
      const q = query(
        collection(db, 'orders'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting user orders:', error);
      throw error;
    }
  },

  // Update order status
  async updateOrderStatus(orderId, status) {
    try {
      const docRef = doc(db, 'orders', orderId);
      await updateDoc(docRef, {
        status,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }
};
