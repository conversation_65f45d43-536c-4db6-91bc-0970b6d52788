import { toast } from 'react-toastify';

// Toast configuration
const toastConfig = {
  position: "top-right",
  autoClose: 4000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
  theme: "light",
};

// Success toast
export const showSuccessToast = (message) => {
  toast.success(message, {
    ...toastConfig,
    className: 'toast-success',
  });
};

// Error toast
export const showErrorToast = (message) => {
  toast.error(message, {
    ...toastConfig,
    className: 'toast-error',
  });
};

// Warning toast
export const showWarningToast = (message) => {
  toast.warning(message, {
    ...toastConfig,
    className: 'toast-warning',
  });
};

// Info toast
export const showInfoToast = (message) => {
  toast.info(message, {
    ...toastConfig,
    className: 'toast-info',
  });
};

// Loading toast
export const showLoadingToast = (message) => {
  return toast.loading(message, {
    ...toastConfig,
    className: 'toast-loading',
  });
};

// Update loading toast
export const updateLoadingToast = (toastId, message, type = 'success') => {
  const updateConfig = {
    ...toastConfig,
    render: message,
    type: type,
    isLoading: false,
    className: `toast-${type}`,
  };
  
  toast.update(toastId, updateConfig);
};

// Dismiss toast
export const dismissToast = (toastId) => {
  toast.dismiss(toastId);
};

// Dismiss all toasts
export const dismissAllToasts = () => {
  toast.dismiss();
};
