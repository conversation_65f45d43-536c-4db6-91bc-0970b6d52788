import { collection, doc, setDoc, getDocs } from 'firebase/firestore';
import { db } from '../firebase/config';
import { sampleProducts, sampleServices, sampleSubscriptionPlans } from '../data/sampleData';

// Initialize database with sample data
export const initializeDatabase = async () => {
  try {
    console.log('Initializing database with sample data...');

    // Check if products already exist
    const productsSnapshot = await getDocs(collection(db, 'products'));
    if (productsSnapshot.empty) {
      console.log('Adding sample products...');
      for (const product of sampleProducts) {
        await setDoc(doc(db, 'products', product.id), {
          ...product,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      console.log('Sample products added successfully!');
    } else {
      console.log('Products already exist, skipping...');
    }

    // Check if services already exist
    const servicesSnapshot = await getDocs(collection(db, 'services'));
    if (servicesSnapshot.empty) {
      console.log('Adding sample services...');
      for (const service of sampleServices) {
        await setDoc(doc(db, 'services', service.id), {
          ...service,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      console.log('Sample services added successfully!');
    } else {
      console.log('Services already exist, skipping...');
    }

    // Check if subscription plans already exist
    const plansSnapshot = await getDocs(collection(db, 'subscriptionPlans'));
    if (plansSnapshot.empty) {
      console.log('Adding sample subscription plans...');
      for (const plan of sampleSubscriptionPlans) {
        await setDoc(doc(db, 'subscriptionPlans', plan.id), {
          ...plan,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      console.log('Sample subscription plans added successfully!');
    } else {
      console.log('Subscription plans already exist, skipping...');
    }

    console.log('Database initialization completed!');
    return { success: true, message: 'Database initialized successfully' };

  } catch (error) {
    console.error('Error initializing database:', error);
    return { success: false, message: 'Failed to initialize database: ' + error.message };
  }
};

// Function to reset database (use with caution)
export const resetDatabase = async () => {
  try {
    console.log('Resetting database...');
    
    // This is a simplified reset - in production, you'd want more sophisticated cleanup
    const collections = ['products', 'services', 'subscriptionPlans'];
    
    for (const collectionName of collections) {
      const snapshot = await getDocs(collection(db, collectionName));
      const deletePromises = snapshot.docs.map(doc => doc.ref.delete());
      await Promise.all(deletePromises);
      console.log(`Cleared ${collectionName} collection`);
    }

    // Re-initialize with sample data
    await initializeDatabase();
    
    console.log('Database reset completed!');
    return { success: true, message: 'Database reset successfully' };

  } catch (error) {
    console.error('Error resetting database:', error);
    return { success: false, message: 'Failed to reset database: ' + error.message };
  }
};

// Function to check database status
export const checkDatabaseStatus = async () => {
  try {
    const collections = ['products', 'services', 'subscriptionPlans'];
    const status = {};

    for (const collectionName of collections) {
      const snapshot = await getDocs(collection(db, collectionName));
      status[collectionName] = {
        exists: !snapshot.empty,
        count: snapshot.size
      };
    }

    return { success: true, status };

  } catch (error) {
    console.error('Error checking database status:', error);
    return { success: false, message: 'Failed to check database status: ' + error.message };
  }
};
