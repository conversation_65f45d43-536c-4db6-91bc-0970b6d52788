import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './styles/toast-custom.css';

// Context Providers
import { AuthProvider } from './contexts/AuthContext';
import { CartProvider } from './contexts/CartContext';
import { ModalProvider } from './contexts/ModalContext';
import { BookingProvider } from './contexts/BookingContext';

// Components
import Preloader from './components/Preloader';
import Header from './components/Header';
import Footer from './components/Footer';
import OffcanvasSidebar from './components/OffcanvasSidebar';
import ProtectedRoute from './components/ProtectedRoute';

// Pages
import Home from './pages/Home';
import About from './pages/About';
import Service from './pages/Service';
import Contact from './pages/Contact';
import Shop from './pages/Shop';
import Cart from './pages/Cart';
import Adoption from './pages/Adoption';
import Gallery from './pages/Gallery';
import Pricing from './pages/Pricing';
import Error from './pages/Error';
import ShopDetails from './pages/ShopDetails';
import Checkout from './pages/Checkout';
import Testimonial from './pages/Testimonial';
import MyAccount from './pages/MyAccount';
import Dashboard from './pages/Dashboard';

function App() {
  return (
    <AuthProvider>
      <BookingProvider>
        <CartProvider>
          <ModalProvider>
            <Router>
          <div className="App">
            <Preloader />
            <Header />

            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/about" element={<About />} />
              <Route path="/service" element={<Service />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/shop" element={<Shop />} />
              <Route path="/cart" element={<Cart />} />
              <Route path="/adoption" element={<Adoption />} />
              <Route path="/gallery" element={<Gallery />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/shop-details/:productId" element={<ShopDetails />} />
              <Route path="/checkout" element={
                <ProtectedRoute>
                  <Checkout />
                </ProtectedRoute>
              } />
              <Route path="/testimonial" element={<Testimonial />} />
              <Route path="/my-account" element={<MyAccount />} />
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } />
              <Route path="/error" element={<Error />} />
              <Route path="*" element={<Error />} />
              {/* Catch-all route for 404 errors */}
            </Routes>

            <Footer />
            <OffcanvasSidebar />
          </div>

          {/* Toast Container */}
          <ToastContainer
            position="top-right"
            autoClose={4000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
            </Router>
          </ModalProvider>
        </CartProvider>
      </BookingProvider>
    </AuthProvider>
  );
}

export default App;
