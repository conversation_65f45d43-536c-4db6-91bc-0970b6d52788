import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useCart } from '../contexts/CartContext';
import { useModal } from '../contexts/ModalContext';
import './Header.css';

const Header = () => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { currentUser, logout } = useAuth();
  const { cartItems } = useCart();
  const { showConfirmModal } = useModal();

  const toggleSearch = (e) => {
    e.preventDefault();
    setIsSearchOpen(!isSearchOpen);
  };

  const toggleProfileDropdown = () => {
    setShowProfileDropdown(!showProfileDropdown);
  };

  const handleLogout = () => {
    showConfirmModal(
      'Confirm Logout',
      'Are you sure you want to log out?',
      async () => {
        try {
          await logout();
          setShowProfileDropdown(false);
          navigate('/');
        } catch (error) {
          console.error('Logout error:', error);
        }
      }
    );
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  const getTotalCartItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  useEffect(() => {
    // Handle body scroll when search overlay is open
    if (isSearchOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup function to reset body overflow when component unmounts
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isSearchOpen]);

  // Close profile dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showProfileDropdown && !event.target.closest('.profile-btn') && !event.target.closest('.profile-dropdown')) {
        setShowProfileDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showProfileDropdown]);

  return (
    <>
      {/* Header Area */}
      <header className="main_header_arae">
        {/* Top Bar */}
        <div className="topbar-area">
          <div className="container">
            <div className="row align-items-center">
              <div className="col-lg-8 col-md-12">
                <div className="top_bar_left_item_wrapper">
                  <div className="top_bar_left_item">
                    <div className="top_Bar_left_icon">
                      <i className="fas fa-envelope"></i>
                    </div>
                    <div className="top_Bar_left_text">
                      <h5><a href="mailto:<EMAIL>"><EMAIL></a></h5>
                      <h6>Get free estimate</h6>
                    </div>
                  </div>
                  <div className="top_bar_left_item">
                    <div className="top_Bar_left_icon">
                      <i className="fas fa-phone"></i>
                    </div>
                    <div className="top_Bar_left_text">
                      <h5><a href="tel:+923131591819">+92 313 1591819</a></h5>
                      <h6>Sat to Fri: 8:00am to 10pm</h6>
                    </div>
                  </div>
                  <div className="top_bar_left_item">
                    <div className="top_Bar_left_icon">
                      <i className="fas fa-map-marker-alt"></i>
                    </div>
                    <div className="top_Bar_left_text">
                      <h5>32, E milad Park, Johar Town.</h5>
                      <h6>Get direction</h6>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 col-md-2">
                  <div className="top_header_social_icon">
                  <div className="social_icon_header_item">
                    <a href="https://facebook.com" target="_blank" rel="noopener noreferrer"> <img src="/img/icon/facebook.png" alt="icon" /></a>
                  </div>
                  <div className="social_icon_header_item">
                    <a href="https://twitter.com" target="_blank" rel="noopener noreferrer"> <img src="/img/icon/twitter.png" alt="icon" /></a>
                  </div>
                  <div className="social_icon_header_item">
                    <a href="https://instagram.com" target="_blank" rel="noopener noreferrer"> <img src="/img/icon/instagram.png" alt="icon" /></a>
                  </div>
                  <div className="social_icon_header_item">
                    <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer"> <img src="/img/icon/linkedin.png" alt="icon" /></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Navbar Bar */}
        <div className="navbar-area">
          <div className="main-responsive-nav">
            <div className="container">
              <div className="main-responsive-menu">
                <div className="logo">
                  <Link to="/" className="d-flex align-items-center">
                    <img src="/img/logo.png" alt="logo" style={{height: '30px', marginRight: '10px'}} />
                  </Link>
                </div>
              </div>
            </div>
          </div>
          <div className="main-navbar">
            <div className="container">
              <nav className="navbar navbar-expand-md navbar-light">
                <Link className="navbar-brand" to="/">
                  <img src="/img/logo.png" alt="logo" style={{height: '40px'}} />
                </Link>
                <div className="collapse navbar-collapse mean-menu" id="navbarSupportedContent">
                  <ul className="navbar-nav">
                    <li className="nav-item">
                      <Link to="/" className={`nav-link ${isActive('/') ? 'active' : ''}`}>
                        Home
                        <i className="fas fa-angle-down"></i>
                      </Link>
                      <ul className="dropdown-menu">
                      </ul>
                    </li>
                    <li className="nav-item">
                      <Link to="/about" className={`nav-link ${isActive('/about') ? 'active' : ''}`}>About us</Link>
                    </li>
                    <li className="nav-item">
                      <a href="/service" className={`nav-link ${isActive('/service') ? 'active' : ''}`}>
                        Services
                        <i className="fas fa-angle-down"></i>
                      </a>
                      <ul className="dropdown-menu">
                        <li className="nav-item">
                          <Link to="/service" className={`nav-link ${isActive('/service') ? 'active' : ''}`}>Service</Link>
                        </li>
                      </ul>
                    </li>
                    <li className="nav-item">
                      <a href="/shop" className={`nav-link ${(isActive('/shop') || isActive('/cart')) ? 'active' : ''}`}>
                        Shop
                        <i className="fas fa-angle-down"></i>
                      </a>
                      <ul className="dropdown-menu">
                        <li className="nav-item">
                          <Link to="/shop" className={`nav-link ${isActive('/shop') ? 'active' : ''}`}>Shop</Link>
                        </li>
                        <li className="nav-item">
                          <Link to="/cart" className={`nav-link ${isActive('/cart') ? 'active' : ''}`}>Cart</Link>
                        </li>
                      </ul>
                    </li>
                    <li className="nav-item">
                      <a href="/adoption" className={`nav-link ${isActive('/adoption') ? 'active' : ''}`}>
                        Adoption
                        <i className="fas fa-angle-down"></i>
                      </a>
                      <ul className="dropdown-menu">
                        <li className="nav-item">
                          <Link to="/adoption" className={`nav-link ${isActive('/adoption') ? 'active' : ''}`}>Adoption</Link>
                        </li>
                      </ul>
                    </li>
                    <li className="nav-item">
                      <a href="/gallery" className={`nav-link ${(isActive('/gallery') || isActive('/pricing') || isActive('/testimonial') || isActive('/my-account') || isActive('/error')) ? 'active' : ''}`}>
                        Pages
                        <i className="fas fa-angle-down"></i>
                      </a>
                      <ul className="dropdown-menu">
                        <li className="nav-item">
                          <Link to="/gallery" className={`nav-link ${isActive('/gallery') ? 'active' : ''}`}>Gallery</Link>
                        </li>
                        <li className="nav-item">
                          <Link to="/pricing" className={`nav-link ${isActive('/pricing') ? 'active' : ''}`}>Pricing</Link>
                        </li>
                        <li className="nav-item">
                          <Link to="/testimonial" className={`nav-link ${isActive('/testimonial') ? 'active' : ''}`}>Testimonial</Link>
                        </li>
                        <li className="nav-item">
                          <Link to="/my-account" className={`nav-link ${isActive('/my-account') ? 'active' : ''}`}>My account</Link>
                        </li>
                        <li className="nav-item">
                          <Link to="/contact" className={`nav-link ${isActive('/contact') ? 'active' : ''}`}>Contact</Link>
                        </li>
                        <li className="nav-item">
                          <Link to="/error" className={`nav-link ${isActive('/error') ? 'active' : ''}`}>404 Error</Link>
                        </li>
                      </ul>
                    </li>
                    <li className="nav-item">
                      <Link to="/contact" className={`nav-link ${isActive('/contact') ? 'active' : ''}`}>Contact</Link>
                    </li>
                  </ul>
                  <div className="others-options d-flex align-items-center">
                    <div className="option-item">
                      <a href="/" className="search-box" onClick={toggleSearch}>
                        <img src="/img/icon/search.png" alt="icon" />
                      </a>
                    </div>

                    {/* Cart Icon */}
                    <div className="option-item position-relative">
                      <Link to="/cart" className="cart-icon d-flex align-items-center">
                        <i className="fas fa-shopping-cart" style={{fontSize: '18px', color: '#333'}}></i>
                        {getTotalCartItems() > 0 && (
                          <span className="cart-badge position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            {getTotalCartItems()}
                          </span>
                        )}
                      </Link>
                    </div>

                    {/* Profile Icon */}
                    {currentUser ? (
                      <div className="option-item position-relative">
                        <button
                          className="profile-btn d-flex align-items-center border-0 bg-transparent"
                          onClick={toggleProfileDropdown}
                        >
                          <i className="fas fa-user-circle" style={{fontSize: '20px', color: '#333'}}></i>
                        </button>

                        {/* Profile Dropdown */}
                        {showProfileDropdown && (
                          <div className="profile-dropdown position-absolute end-0 mt-2 bg-white shadow rounded" style={{minWidth: '200px', zIndex: 1000}}>
                            <div className="p-3 border-bottom">
                              <div className="fw-bold">{currentUser.displayName || 'User'}</div>
                              <small className="text-muted">{currentUser.email}</small>
                            </div>
                            <div className="py-1">
                              <Link
                                to="/dashboard"
                                className="dropdown-item d-flex align-items-center px-3 py-2 text-decoration-none text-dark"
                                onClick={() => setShowProfileDropdown(false)}
                              >
                                <i className="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                              </Link>
                              <Link
                                to="/my-account"
                                className="dropdown-item d-flex align-items-center px-3 py-2 text-decoration-none text-dark"
                                onClick={() => setShowProfileDropdown(false)}
                              >
                                <i className="fas fa-user me-2"></i>
                                My Account
                              </Link>
                              <hr className="dropdown-divider mx-3" />
                              <button
                                className="dropdown-item d-flex align-items-center px-3 py-2 text-decoration-none text-danger border-0 bg-transparent w-100"
                                onClick={handleLogout}
                              >
                                <i className="fas fa-sign-out-alt me-2"></i>
                                Logout
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="option-item">
                        <Link to="/my-account" className="login-btn d-flex align-items-center text-decoration-none">
                          <i className="fas fa-user" style={{fontSize: '18px', color: '#333'}}></i>
                          <span className="ms-1" style={{color: '#333', fontSize: '14px'}}>Login</span>
                        </Link>
                      </div>
                    )}

                    <div className="option-item">
                      <a href="/" data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight"
                        aria-controls="offcanvasRight">
                        <img src="/img/icon/menu.png" alt="icon" />
                      </a>
                    </div>
                  </div>
                </div>
              </nav>
            </div>
          </div>
          <div className="others-option-for-responsive">
            <div className="container">
              <div className="dot-menu">
                <div className="inner">
                  <div className="circle circle-one"></div>
                  <div className="circle circle-two"></div>
                  <div className="circle circle-three"></div>
                </div>
              </div>
              <div className="container">
                <div className="option-inner">
                  <div className="others-options">
                    <div className="responsive_icon_dot_flex">
                      <div className="option-item">
                        <a href="/" className="search-box" onClick={toggleSearch}> 
                          <img src="/img/icon/search.png" alt="icon" />
                        </a>
                      </div>
                      <div className="option-item">
                        <a href="/" data-bs-toggle="offcanvas" data-bs-target="#offcanvasRight"
                          aria-controls="offcanvasRight"> 
                          <img src="/img/icon/menu.png" alt="icon" />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Search Overlay */}
      <div className={`search-overlay ${isSearchOpen ? 'search-overlay-active' : ''}`}>
        <div className="d-table">
          <div className="d-table-cell">
            <div className="search-overlay-layer"></div>
            <div className="search-overlay-layer"></div>
            <div className="search-overlay-layer"></div>
            <div className="search-overlay-close" onClick={toggleSearch}>
              <span className="search-overlay-close-line"></span>
              <span className="search-overlay-close-line"></span>
            </div>
            <div className="search-overlay-form">
              <form>
                <input type="text" className="input-search" placeholder="Search here..." />
                <button type="button"><i className="fas fa-search"></i></button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Header;
