import React from 'react';

const OffcanvasSidebar = () => {
  return (
    <div className="offcanvas offcanvas-end offcanvas_custom" tabIndex="-1" id="offcanvasRight">
      <div className="offcanvas-header">
        <img src="/img/logo.png" alt="img" />
        <button type="button" className="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div className="offcanvas-body">
        <div className="offcanvas_right_wrapper">
          <p>Welcome to PetsEnvy! We're here to provide the best care and services for your beloved pets.</p>
          <h4>Contact Us</h4>
          <div className="top_bar_left_item">
            <div className="top_Bar_left_icon">
              <i className="fas fa-envelope"></i>
            </div>
            <div className="top_Bar_left_text">
              <h5><a href="mailto:<EMAIL>"><EMAIL></a></h5>
              <h6>Get free estimate</h6>
            </div>
          </div>
          <div className="top_bar_left_item">
            <div className="top_Bar_left_icon">
              <i className="fas fa-phone"></i>
            </div>
            <div className="top_Bar_left_text">
              <h5><a href="tel:+923131591819">+92 3131591819</a></h5>
              <h6>Sat to Fri: 8:00am to 10:00pm</h6>
            </div>
          </div>
          <div className="top_bar_left_item">
            <div className="top_Bar_left_icon">
              <i className="fas fa-map-marker-alt"></i>
            </div>
            <div className="top_Bar_left_text">
              <h5>32, E Milad Park, Johar Town.</h5>
              <h6>Get direction</h6>
            </div>
          </div>
          <div className="offcanvas_follow_area">
            <h5>Follow Us</h5>
            <ul>
              <li><a href="#!"><img src="/img/icon/facebook.png" alt="icon" /></a></li>
              <li><a href="#!"><img src="/img/icon/twitter.png" alt="icon" /></a></li>
              <li><a href="#!"><img src="/img/icon/instagram.png" alt="icon" /></a></li>
              <li><a href="#!"><img src="/img/icon/linkedin.png" alt="icon" /></a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OffcanvasSidebar;
