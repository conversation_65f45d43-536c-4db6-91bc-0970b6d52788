import React from 'react';
import './CustomModal.css';

const CustomModal = ({ 
  isOpen, 
  onClose, 
  title, 
  message, 
  type = 'info', // 'success', 'error', 'warning', 'info', 'confirm'
  confirmText = 'OK',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  showCancel = false
}) => {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <i className="fas fa-check-circle modal-icon success"></i>;
      case 'error':
        return <i className="fas fa-times-circle modal-icon error"></i>;
      case 'warning':
        return <i className="fas fa-exclamation-triangle modal-icon warning"></i>;
      case 'confirm':
        return <i className="fas fa-question-circle modal-icon confirm"></i>;
      default:
        return <i className="fas fa-info-circle modal-icon info"></i>;
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    } else {
      onClose();
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      onClose();
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="custom-modal-overlay" onClick={handleBackdropClick}>
      <div className="custom-modal">
        <div className="custom-modal-header">
          <button className="custom-modal-close" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>
        
        <div className="custom-modal-body">
          <div className="custom-modal-icon-container">
            {getIcon()}
          </div>
          
          {title && <h3 className="custom-modal-title">{title}</h3>}
          
          <div className="custom-modal-message">
            {typeof message === 'string' ? <p>{message}</p> : message}
          </div>
        </div>
        
        <div className="custom-modal-footer">
          {showCancel && (
            <button 
              className="custom-modal-btn custom-modal-btn-cancel" 
              onClick={handleCancel}
            >
              {cancelText}
            </button>
          )}
          <button 
            className={`custom-modal-btn custom-modal-btn-confirm ${type}`} 
            onClick={handleConfirm}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomModal;
