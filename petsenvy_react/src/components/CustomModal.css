/* Custom Modal Styles */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
  animation: modalOverlayFadeIn 0.3s ease-out;
}

@keyframes modalOverlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.custom-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 450px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.custom-modal-header {
  position: relative;
  padding: 15px 20px 0;
}

.custom-modal-close {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-modal-close:hover {
  background-color: #f5f5f5;
  color: #333;
}

.custom-modal-body {
  padding: 20px 30px;
  text-align: center;
}

.custom-modal-icon-container {
  margin-bottom: 20px;
}

.modal-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.modal-icon.success {
  color: #28a745;
}

.modal-icon.error {
  color: #dc3545;
}

.modal-icon.warning {
  color: #ffc107;
}

.modal-icon.info {
  color: #17a2b8;
}

.modal-icon.confirm {
  color: #6c757d;
}

.custom-modal-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
  line-height: 1.3;
}

.custom-modal-message {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
}

.custom-modal-message p {
  margin: 0;
}

.custom-modal-footer {
  padding: 0 30px 30px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.custom-modal-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.custom-modal-btn-cancel {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.custom-modal-btn-cancel:hover {
  background-color: #e9ecef;
  color: #495057;
}

.custom-modal-btn-confirm {
  color: white;
  border: none;
}

.custom-modal-btn-confirm.success {
  background-color: #28a745;
}

.custom-modal-btn-confirm.success:hover {
  background-color: #218838;
}

.custom-modal-btn-confirm.error {
  background-color: #dc3545;
}

.custom-modal-btn-confirm.error:hover {
  background-color: #c82333;
}

.custom-modal-btn-confirm.warning {
  background-color: #ffc107;
  color: #212529;
}

.custom-modal-btn-confirm.warning:hover {
  background-color: #e0a800;
}

.custom-modal-btn-confirm.info {
  background-color: #17a2b8;
}

.custom-modal-btn-confirm.info:hover {
  background-color: #138496;
}

.custom-modal-btn-confirm.confirm {
  background-color: #007bff;
}

.custom-modal-btn-confirm.confirm:hover {
  background-color: #0056b3;
}

/* Responsive Design */
@media (max-width: 576px) {
  .custom-modal {
    margin: 20px;
    width: calc(100% - 40px);
  }
  
  .custom-modal-body {
    padding: 20px;
  }
  
  .custom-modal-footer {
    padding: 0 20px 20px;
    flex-direction: column;
  }
  
  .custom-modal-btn {
    width: 100%;
  }
  
  .modal-icon {
    font-size: 40px;
  }
  
  .custom-modal-title {
    font-size: 20px;
  }
}
