import React from 'react';
import { useModal } from '../contexts/ModalContext';

const FirebaseSetupHelper = () => {
  const { showInfoModal } = useModal();

  const showSetupInstructions = () => {
    const instructions = (
      <div style={{ textAlign: 'left', lineHeight: '1.6' }}>
        <p><strong>Firebase Authentication is not enabled. Please follow these steps:</strong></p>
        <ol style={{ paddingLeft: '20px', margin: '10px 0' }}>
          <li>Go to <a href="https://console.firebase.google.com/" target="_blank" rel="noopener noreferrer" style={{ color: '#007bff' }}>Firebase Console</a></li>
          <li>Select your project: <strong>pawsenvy-a1541</strong></li>
          <li>Click on <strong>"Authentication"</strong> in the sidebar</li>
          <li>Go to <strong>"Sign-in method"</strong> tab</li>
          <li>Find <strong>"Email/Password"</strong> and click on it</li>
          <li>Toggle <strong>"Enable"</strong> to ON</li>
          <li>Click <strong>"Save"</strong></li>
          <li>Refresh this page and try again</li>
        </ol>
        <p style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px', fontSize: '14px' }}>
          <strong>Note:</strong> This is a one-time setup required for your Firebase project.
        </p>
      </div>
    );

    showInfoModal(
      'Firebase Setup Required',
      instructions
    );
  };

  return {
    showSetupInstructions
  };
};

export default FirebaseSetupHelper;
