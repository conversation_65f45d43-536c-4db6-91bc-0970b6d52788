/* Header Custom Styles */

/* Cart Badge */
.cart-badge {
  font-size: 10px !important;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(50%, -50%) !important;
}

.cart-icon {
  position: relative;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.cart-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Profile Dropdown */
.profile-btn {
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.profile-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.profile-dropdown {
  border: 1px solid #dee2e6;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-divider {
  margin: 0.5rem 0;
  border-top: 1px solid #dee2e6;
}

/* Login Button */
.login-btn {
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.login-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Option Items Spacing */
.others-options .option-item {
  margin-left: 15px;
}

.others-options .option-item:first-child {
  margin-left: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .others-options .option-item {
    margin-left: 10px;
  }
  
  .cart-icon,
  .profile-btn,
  .login-btn {
    padding: 6px;
  }
  
  .profile-dropdown {
    right: -20px;
    min-width: 180px;
  }
}

/* Close dropdown when clicking outside */
.profile-dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
