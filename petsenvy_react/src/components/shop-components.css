/* Price Slider Styles */
.price-slider-wrapper {
  padding: 20px 0;
}

.price-slider {
  width: 100%;
  height: 5px;
  border-radius: 5px;
  background: #ddd;
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.price-slider:hover {
  opacity: 1;
}

.price-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ff6b35;
  cursor: pointer;
}

.price-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ff6b35;
  cursor: pointer;
  border: none;
}

.price-range-display {
  text-align: center;
  margin-top: 10px;
  font-weight: 600;
  color: #333;
}

.price-range-display span {
  background: #f8f9fa;
  padding: 5px 15px;
  border-radius: 20px;
  border: 1px solid #e9ecef;
}
