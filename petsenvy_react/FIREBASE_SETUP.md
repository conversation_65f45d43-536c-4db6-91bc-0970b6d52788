# 🔥 Firebase Authentication Setup Guide

## ⚠️ **IMPORTANT: Enable Email/Password Authentication**

The `auth/operation-not-allowed` error occurs because Email/Password authentication is not enabled in your Firebase project. Follow these steps to fix it:

### **Step 1: Access Firebase Console**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **pawsenvy-a1541**

### **Step 2: Enable Authentication**
1. In the left sidebar, click on **"Authentication"**
2. Click on the **"Sign-in method"** tab
3. Find **"Email/Password"** in the list of providers
4. Click on **"Email/Password"**
5. Toggle **"Enable"** to ON
6. Click **"Save"**

### **Step 3: Configure Authentication Settings**
1. In the **"Settings"** tab of Authentication:
   - Set your **"Authorized domains"** (add `localhost` for development)
   - Configure **"User actions"** if needed

### **Step 4: Firestore Database Setup**
1. Go to **"Firestore Database"** in the sidebar
2. Click **"Create database"**
3. Choose **"Start in test mode"** for development
4. Select your preferred location
5. Click **"Done"**

### **Step 5: Security Rules (Optional for Development)**
For development, you can use these permissive rules:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🛠️ **Current Firebase Configuration**

Your project is configured with:
- **Project ID**: pawsenvy-a1541
- **API Key**: AIzaSyARwnRsoQiJ9-vGHSfP9a2zxr9MPLdXv6A
- **Auth Domain**: pawsenvy-a1541.firebaseapp.com
- **Database URL**: https://pawsenvy-a1541-default-rtdb.firebaseio.com
- **Storage Bucket**: pawsenvy-a1541.appspot.com

## ✅ **Verification Steps**

After enabling authentication:
1. Try creating a new account on your website
2. Check the Firebase Console > Authentication > Users to see if the user was created
3. Test login functionality
4. Test password reset functionality

## 🚨 **Common Issues & Solutions**

### **Issue**: `auth/operation-not-allowed`
**Solution**: Enable Email/Password authentication in Firebase Console

### **Issue**: `auth/invalid-api-key`
**Solution**: Check your Firebase configuration in `src/firebase/config.js`

### **Issue**: `auth/network-request-failed`
**Solution**: Check your internet connection and Firebase project status

### **Issue**: Firestore permission denied
**Solution**: Update Firestore security rules or ensure user is authenticated

## 📱 **Testing Authentication**

1. **Registration**: Try creating a new account with email/password
2. **Login**: Test login with the created account
3. **Password Reset**: Test forgot password functionality
4. **Logout**: Ensure logout works properly
5. **Protected Routes**: Test that protected pages require authentication

## 🔧 **Development vs Production**

### **Development Settings**:
- Test mode Firestore rules
- Localhost in authorized domains
- Debug mode enabled

### **Production Settings**:
- Secure Firestore rules
- Production domain in authorized domains
- Debug mode disabled
- Enable App Check for additional security

## 📞 **Support**

If you continue to experience issues:
1. Check the browser console for detailed error messages
2. Verify your Firebase project settings
3. Ensure your Firebase plan supports the features you're using
4. Check Firebase status page for any ongoing issues

## 🎯 **Next Steps After Setup**

Once authentication is working:
1. Test all authentication flows
2. Set up proper Firestore security rules
3. Configure email templates for password reset
4. Set up email verification (optional)
5. Configure social login providers (optional)

---

**Note**: This setup guide is specifically for the PetsEnvy project using Firebase project `pawsenvy-a1541`.
