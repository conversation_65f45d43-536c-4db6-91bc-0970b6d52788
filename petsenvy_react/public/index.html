<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/img/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="PetsEnvy - The World Best Team For Pet Care Services"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />

    <!-- Bootstrap css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/bootstrap.min.css">
    <!-- animate css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/animate.min.css">
    <!-- Fontawesome css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/fontawesome.all.min.css">
    <!-- owl.carousel css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/owl.carousel.min.css">
    <!-- owl.theme.default css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/owl.theme.default.min.css">
    <!-- Slick css -->
    <link rel="stylesheet" type="text/css" href="%PUBLIC_URL%/css/slick.min.css">
    <!-- Magnific popup css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/magnific-popup.min.css">
    <!-- navber css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/navber.css">
    <!-- meanmenu css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/meanmenu.css">
    <!-- Style css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/style.css">
    <!-- Responsive css -->
    <link rel="stylesheet" href="%PUBLIC_URL%/css/responsive.css">

    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>PetsEnvy - Pet Care Services</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->

    <!-- JavaScript files -->
    <script src="%PUBLIC_URL%/js/jquery.min.js"></script>
    <!-- Bootstrap js -->
    <script src="%PUBLIC_URL%/js/bootstrap.bundle.js"></script>
    <!-- Meanu js -->
    <script src="%PUBLIC_URL%/js/jquery.meanmenu.js"></script>
    <!-- Magnific Popup js -->
    <script src="%PUBLIC_URL%/js/jquery.magnific-popup.min.js"></script>
    <!-- owl carousel js -->
    <script src="%PUBLIC_URL%/js/owl.carousel.min.js"></script>
    <!-- Slick js -->
    <script src="%PUBLIC_URL%/js/slick.min.js"></script>
    <script src="%PUBLIC_URL%/js/slick-slider.js"></script>
    <!-- wow.js -->
    <script src="%PUBLIC_URL%/js/wow.min.js"></script>
    <!-- waypoints.js -->
    <script src="%PUBLIC_URL%/js/waypoints.min.js"></script>
    <!-- counterup.js -->
    <script src="%PUBLIC_URL%/js/jquery.counterup.min.js"></script>
    <!-- Custom js -->
    <script src="%PUBLIC_URL%/js/gallery-popup.js"></script>
    <script src="%PUBLIC_URL%/js/custom.js"></script>
    <script src="%PUBLIC_URL%/js/video.js"></script>
  </body>
</html>
